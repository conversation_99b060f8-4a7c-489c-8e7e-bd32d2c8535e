#ifndef DEBUG_CONFIG_H
#define DEBUG_CONFIG_H

#include <stdio.h>

/* =============================================================================
 * 调试开关配置
 * =============================================================================
 */

// 主调试开关 - 控制所有调试输出
#define DEBUG_ENABLE                    1

// 关键系统调试 - 即使关闭主调试也保持开启
#define DEBUG_CRITICAL_SYSTEM           0   // 关键系统信息，始终输出

// 通讯模块调试开关
#define DEBUG_MODBUS_COMMUNICATION      0   // Modbus通信调试
#define DEBUG_MODBUS_RAW_DATA           0   // 原始Modbus数据调试
#define DEBUG_MODBUS_PARSED_DATA        0   // 解析后数据调试
#define DEBUG_MODBUS_ECHO_DATA          1   // 回波数据调试

// 算法模块调试开关
#define DEBUG_ALGORITHM_PROCESSING      1   // 算法处理调试
#define DEBUG_E52436_DECISION           0   // E52436决策逻辑调试
#define DEBUG_DISTANCE_CALCULATION      0   // 距离计算调试
#define DEBUG_COORDINATE_CALCULATION    0   // 坐标计算调试
#define DEBUG_VALUE_UPDATE              0   // 数值更新调试
#define DEBUG_FILTER_ALGORITHM          1   // 滤波算法调试

// 系统状态调试开关
#define DEBUG_SENSOR_ERROR_STATE        0   // 传感器错误状态调试
#define DEBUG_NOISE_SUPPRESSION         0   // 噪声抑制调试
#define DEBUG_ROBOT_DIRECTION_FUSION    0   // 机器人方向融合调试

// 测试调试开关
#define DEBUG_SIMPLE_DISTANCE_TEST      0   // 简单距离测试调试

/* =============================================================================
 * 算法优化开关
 * =============================================================================
 */

// 滤波算法优化开关 - 回归原始E52436移植
#define ENABLE_ADAPTIVE_FILTERING       0   // 禁用自适应滤波
#define ENABLE_FIRST_RUN_DETECTION      0   // 禁用第一次运行检测
#define ENABLE_COORDINATE_BACKUP        0   // 禁用坐标计算备用方案
#define ENABLE_NOISE_THRESHOLD_ADJUST   1   // 禁用噪声阈值调整
#define ENABLE_FILTER_BYPASS           1   // 启用滤波绕过（合理范围内）

// 传感器错误处理优化 - 回归原始E52436移植
#define ENABLE_SENSOR_ERROR_TOLERANCE   1   // 禁用传感器错误容错
#define ENABLE_DISTANCE_PRESERVATION    1   // 禁用距离数据保护

/* =============================================================================
 * 调试宏定义
 * =============================================================================
 */

#if DEBUG_ENABLE

#define DEBUG_PRINT(fmt, ...) \
    do { \
        printf(fmt, ##__VA_ARGS__); \
    } while(0)

// 通讯模块调试宏
#if DEBUG_MODBUS_COMMUNICATION
#define DEBUG_MODBUS(fmt, ...) DEBUG_PRINT("MODBUS_DEBUG: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS(fmt, ...)
#endif

#if DEBUG_MODBUS_RAW_DATA
#define DEBUG_MODBUS_RAW(fmt, ...) DEBUG_PRINT("RAW_MODBUS_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_RAW(fmt, ...)
#endif

#if DEBUG_MODBUS_PARSED_DATA
#define DEBUG_MODBUS_PARSED(fmt, ...) DEBUG_PRINT("PARSED_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_PARSED(fmt, ...)
#endif

#if DEBUG_MODBUS_ECHO_DATA
#define DEBUG_MODBUS_ECHO(fmt, ...) DEBUG_PRINT("ECHO_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_ECHO(fmt, ...)
#endif

// 算法模块调试宏
#if DEBUG_ALGORITHM_PROCESSING
#define DEBUG_ALGORITHM(fmt, ...) DEBUG_PRINT("ALGORITHM: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_ALGORITHM(fmt, ...)
#endif

#if DEBUG_E52436_DECISION
#define DEBUG_E52436(fmt, ...) DEBUG_PRINT("E52436_DECISION: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_E52436(fmt, ...)
#endif

// 通用警告调试宏
#if DEBUG_MODBUS_COMMUNICATION
#define DEBUG_MODBUS_WARNING(fmt, ...) DEBUG_PRINT("MODBUS_WARNING: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_WARNING(fmt, ...)
#endif

// 距离计算调试
#if DEBUG_DISTANCE_CALCULATION
#define DEBUG_DISTANCE(fmt, ...) DEBUG_PRINT("DEBUG_DISTANCE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_DISTANCE(fmt, ...)
#endif

// 坐标计算调试
#if DEBUG_COORDINATE_CALCULATION
#define DEBUG_COORDINATE(fmt, ...) DEBUG_PRINT("DEBUG_COORDINATE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_COORDINATE(fmt, ...)
#endif

// 数值更新调试
#if DEBUG_VALUE_UPDATE
#define DEBUG_VALUE(fmt, ...) DEBUG_PRINT("DEBUG_VALUE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_VALUE(fmt, ...)
#endif

// 噪声抑制调试
#if DEBUG_NOISE_SUPPRESSION
#define DEBUG_NOISE(fmt, ...) DEBUG_PRINT("DEBUG_NOISE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_NOISE(fmt, ...)
#endif

// 滤波算法调试
#if DEBUG_FILTER_ALGORITHM
#define DEBUG_FILTER(fmt, ...) DEBUG_PRINT("DEBUG_FILTER: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_FILTER(fmt, ...)
#endif

// 传感器错误状态调试
#if DEBUG_SENSOR_ERROR_STATE
#define DEBUG_SENSOR_ERROR(fmt, ...) DEBUG_PRINT("DEBUG_SENSOR_ERROR: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_SENSOR_ERROR(fmt, ...)
#endif

// 机器人方向融合调试
#if DEBUG_ROBOT_DIRECTION_FUSION
#define DEBUG_ROBOT(fmt, ...) DEBUG_PRINT("DEBUG_ROBOT: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_ROBOT(fmt, ...)
#endif

// 简单距离测试调试
#if DEBUG_SIMPLE_DISTANCE_TEST
#define DEBUG_SIMPLE_TEST(fmt, ...) DEBUG_PRINT("DEBUG_SIMPLE_TEST: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_SIMPLE_TEST(fmt, ...)
#endif

// 关键系统调试 - 始终启用
#if DEBUG_CRITICAL_SYSTEM
#define DEBUG_CRITICAL(fmt, ...) \
    do { \
        printf("CRITICAL: " fmt, ##__VA_ARGS__); \
    } while(0)
#else
#define DEBUG_CRITICAL(fmt, ...)
#endif

#else
// 如果主调试开关关闭，所有调试输出都被禁用
#define DEBUG_PRINT(fmt, ...)
#define DEBUG_MODBUS(fmt, ...)
#define DEBUG_MODBUS_RAW(fmt, ...)
#define DEBUG_MODBUS_PARSED(fmt, ...)
#define DEBUG_MODBUS_ECHO(fmt, ...)
#define DEBUG_MODBUS_WARNING(fmt, ...)
#define DEBUG_ALGORITHM(fmt, ...)
#define DEBUG_E52436(fmt, ...)
#define DEBUG_DISTANCE(fmt, ...)
#define DEBUG_COORDINATE(fmt, ...)
#define DEBUG_VALUE(fmt, ...)
#define DEBUG_NOISE(fmt, ...)
#define DEBUG_FILTER(fmt, ...)
#define DEBUG_SENSOR_ERROR(fmt, ...)
#define DEBUG_ROBOT(fmt, ...)
#define DEBUG_SIMPLE_TEST(fmt, ...)

// 关键系统调试 - 即使主调试关闭也保持
#if DEBUG_CRITICAL_SYSTEM
#define DEBUG_CRITICAL(fmt, ...) \
    do { \
        printf("CRITICAL: " fmt, ##__VA_ARGS__); \
    } while(0)
#else
#define DEBUG_CRITICAL(fmt, ...)
#endif

#endif

/* =============================================================================
 * 算法参数配置 - 基于E52436原始设计
 * =============================================================================
 */

// E52436侦听数据验证参数
#define LISTEN_DISTANCE_THRESHOLD_STRICT    50   // 严格模式距离差阈值 (cm)
#define LISTEN_DISTANCE_THRESHOLD_NORMAL    100  // 正常模式距离差阈值 (cm)
#define LISTEN_DISTANCE_THRESHOLD_RELAXED   200  // 宽松模式距离差阈值 (cm)

// E52436电压验证参数
//#define MIN_VOLTAGE_THRESHOLD           30   // 最小电压阈值
//#define NORMAL_VOLTAGE_THRESHOLD        80   // 正常电压阈值
//#define VOLTAGE_QUALITY_THRESHOLD       50   // 电压质量阈值

// E52436传感器几何参数
//#define SENSOR_SPACING_DEFAULT          100  // 默认传感器间距 (mm)
//#define CENTER_CENTER                   150  // 中心传感器间距 (mm)
//#define PLAT_COR_CEN_DIS               120  // 平台角落到中心距离 (mm)

// 噪声抑制参数
#define NOISE_THRESHOLD_DEFAULT         8   // 默认噪声阈值
#define NOISE_THRESHOLD_RELAXED         15  // 放宽的噪声阈值

// 滤波参数
#define FILTER_BYPASS_MIN_DISTANCE      5   // 滤波绕过最小距离 (cm)
#define FILTER_BYPASS_MAX_DISTANCE      200 // 滤波绕过最大距离 (cm)

// 传感器错误容错参数
#define SENSOR_ERROR_TOLERANCE_LEVEL    2   // 传感器错误容错等级

/* =============================================================================
 * 环境配置 - 四传感器一排，前方40cm障碍物
 * =============================================================================
 */

// 传感器布局配置
#define SENSOR_LAYOUT_LINEAR            1   // 线性排列
#define SENSOR_COUNT_ACTIVE             4   // 活跃传感器数量
#define OBSTACLE_DISTANCE_CM            40  // 前方障碍物距离

// 针对当前环境的优化参数
#define EXPECTED_OBSTACLE_DISTANCE      40  // 预期障碍物距离 (cm)
#define DISTANCE_TOLERANCE_CM           5   // 距离容差 (cm)
#define MEASUREMENT_STABILITY_THRESHOLD 3   // 测量稳定性阈值

#endif /* DEBUG_CONFIG_H */
