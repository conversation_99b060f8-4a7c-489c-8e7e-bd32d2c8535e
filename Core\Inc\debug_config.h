#ifndef DEBUG_CONFIG_H
#define DEBUG_CONFIG_H

#include <stdio.h>

/* =============================================================================
 * 调试开关配置
 * =============================================================================
 */

// 主调试开关 - 控制所有调试输出
#define DEBUG_ENABLE                    0

// 关键系统调试 - 即使关闭主调试也保持开启
#define DEBUG_CRITICAL_SYSTEM           0   // 关键系统信息，始终输出

// 分模块调试开关
#define DEBUG_MODBUS_COMMUNICATION      1   // Modbus通信调试
#define DEBUG_DISTANCE_CALCULATION      1   // 距离计算调试
#define DEBUG_COORDINATE_CALCULATION    1   // 坐标计算调试
#define DEBUG_VALUE_UPDATE              1   // 数值更新调试
#define DEBUG_NOISE_SUPPRESSION         1   // 噪声抑制调试
#define DEBUG_FILTER_ALGORITHM          1   // 滤波算法调试
#define DEBUG_SENSOR_ERROR_STATE        1   // 传感器错误状态调试
#define DEBUG_ROBOT_DIRECTION_FUSION    1   // 机器人方向融合调试
#define DEBUG_SIMPLE_DISTANCE_TEST      1   // 简单距离测试调试

/* =============================================================================
 * 算法优化开关
 * =============================================================================
 */

// 滤波算法优化开关
#define ENABLE_ADAPTIVE_FILTERING       1   // 启用自适应滤波
#define ENABLE_FIRST_RUN_DETECTION      1   // 启用第一次运行检测
#define ENABLE_COORDINATE_BACKUP        1   // 启用坐标计算备用方案
#define ENABLE_NOISE_THRESHOLD_ADJUST   1   // 启用噪声阈值调整
#define ENABLE_FILTER_BYPASS           1   // 启用滤波绕过（合理范围内）

// 传感器错误处理优化
#define ENABLE_SENSOR_ERROR_TOLERANCE   1   // 启用传感器错误容错
#define ENABLE_DISTANCE_PRESERVATION    1   // 启用距离数据保护

/* =============================================================================
 * 调试宏定义
 * =============================================================================
 */

#if DEBUG_ENABLE

#define DEBUG_PRINT(fmt, ...) \
    do { \
        printf(fmt, ##__VA_ARGS__); \
    } while(0)

// Modbus通信调试
#if DEBUG_MODBUS_COMMUNICATION
#define DEBUG_MODBUS(fmt, ...) DEBUG_PRINT("DEBUG_MODBUS: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS(fmt, ...)
#endif

// 距离计算调试
#if DEBUG_DISTANCE_CALCULATION
#define DEBUG_DISTANCE(fmt, ...) DEBUG_PRINT("DEBUG_DISTANCE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_DISTANCE(fmt, ...)
#endif

// 坐标计算调试
#if DEBUG_COORDINATE_CALCULATION
#define DEBUG_COORDINATE(fmt, ...) DEBUG_PRINT("DEBUG_COORDINATE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_COORDINATE(fmt, ...)
#endif

// 数值更新调试
#if DEBUG_VALUE_UPDATE
#define DEBUG_VALUE(fmt, ...) DEBUG_PRINT("DEBUG_VALUE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_VALUE(fmt, ...)
#endif

// 噪声抑制调试
#if DEBUG_NOISE_SUPPRESSION
#define DEBUG_NOISE(fmt, ...) DEBUG_PRINT("DEBUG_NOISE: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_NOISE(fmt, ...)
#endif

// 滤波算法调试
#if DEBUG_FILTER_ALGORITHM
#define DEBUG_FILTER(fmt, ...) DEBUG_PRINT("DEBUG_FILTER: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_FILTER(fmt, ...)
#endif

// 传感器错误状态调试
#if DEBUG_SENSOR_ERROR_STATE
#define DEBUG_SENSOR_ERROR(fmt, ...) DEBUG_PRINT("DEBUG_SENSOR_ERROR: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_SENSOR_ERROR(fmt, ...)
#endif

// 机器人方向融合调试
#if DEBUG_ROBOT_DIRECTION_FUSION
#define DEBUG_ROBOT(fmt, ...) DEBUG_PRINT("DEBUG_ROBOT: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_ROBOT(fmt, ...)
#endif

// 简单距离测试调试
#if DEBUG_SIMPLE_DISTANCE_TEST
#define DEBUG_SIMPLE_TEST(fmt, ...) DEBUG_PRINT("DEBUG_SIMPLE_TEST: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_SIMPLE_TEST(fmt, ...)
#endif

// 关键系统调试 - 始终启用
#if DEBUG_CRITICAL_SYSTEM
#define DEBUG_CRITICAL(fmt, ...) \
    do { \
        printf("CRITICAL: " fmt, ##__VA_ARGS__); \
    } while(0)
#else
#define DEBUG_CRITICAL(fmt, ...)
#endif

#else
// 如果主调试开关关闭，所有调试输出都被禁用
#define DEBUG_PRINT(fmt, ...)
#define DEBUG_MODBUS(fmt, ...)
#define DEBUG_DISTANCE(fmt, ...)
#define DEBUG_COORDINATE(fmt, ...)
#define DEBUG_VALUE(fmt, ...)
#define DEBUG_NOISE(fmt, ...)
#define DEBUG_FILTER(fmt, ...)
#define DEBUG_SENSOR_ERROR(fmt, ...)
#define DEBUG_ROBOT(fmt, ...)
#define DEBUG_SIMPLE_TEST(fmt, ...)

// 关键系统调试 - 即使主调试关闭也保持
#if DEBUG_CRITICAL_SYSTEM
#define DEBUG_CRITICAL(fmt, ...) \
    do { \
        printf("CRITICAL: " fmt, ##__VA_ARGS__); \
    } while(0)
#else
#define DEBUG_CRITICAL(fmt, ...)
#endif

#endif

/* =============================================================================
 * 算法参数配置
 * =============================================================================
 */

// 噪声抑制参数
#define NOISE_THRESHOLD_DEFAULT         8   // 默认噪声阈值
#define NOISE_THRESHOLD_RELAXED         15  // 放宽的噪声阈值

// 滤波参数
#define FILTER_BYPASS_MIN_DISTANCE      5   // 滤波绕过最小距离 (cm)
#define FILTER_BYPASS_MAX_DISTANCE      200 // 滤波绕过最大距离 (cm)

// 传感器错误容错参数
#define SENSOR_ERROR_TOLERANCE_LEVEL    2   // 传感器错误容错等级

/* =============================================================================
 * 环境配置 - 四传感器一排，前方40cm障碍物
 * =============================================================================
 */

// 传感器布局配置
#define SENSOR_LAYOUT_LINEAR            1   // 线性排列
#define SENSOR_COUNT_ACTIVE             4   // 活跃传感器数量
#define OBSTACLE_DISTANCE_CM            40  // 前方障碍物距离

// 针对当前环境的优化参数
#define EXPECTED_OBSTACLE_DISTANCE      40  // 预期障碍物距离 (cm)
#define DISTANCE_TOLERANCE_CM           5   // 距离容差 (cm)
#define MEASUREMENT_STABILITY_THRESHOLD 3   // 测量稳定性阈值

#endif /* DEBUG_CONFIG_H */
