#include "modbus_interface.h"
#include "cmsis_os.h"
#include "echo_processing.h"
#include "main.h"
#include "debug_config.h"


// 示例1: 读取测距值
uint32_t result = 0;

// 清理Modbus状态的辅助函数
void ClearModbusState(modbusHandler_t *modH) {
    // 清理接收缓冲区
    RingClear(&modH->xBufferRX);

    // 重置状态
    modH->i8state = COM_IDLE;
    modH->i8lastError = ERR_NO_ERR;
    modH->u8BufferSize = 0;

    // 清理任务通知
    ulTaskNotifyTake(pdTRUE, 0); // 非阻塞清理

    DEBUG_MODBUS("Modbus state cleared\r\n");
}

int16_t read_distance(modbusHandler_t *modH, uint8_t slave_addr, uint16_t *distance) {
    modbus_t read_cmd = {
        .u8id = slave_addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = REG_DISTANCE, // 测距值寄存器地址
        .u16CoilsNo = 1,     // 读取1个寄存器
        .u16reg = NULL
    };

    result = ModbusQueryV2(modH, read_cmd);
    if(result == OP_OK_QUERY) {
        *distance = modH->u16regs[0]; // 读取结果存储在u16regs[0]
        return MODBUS_Q_OK;
    }
    return MODBUS_Q_ERR; // 错误
}

// 示例2: 读取多个寄存器
int16_t read_multiple_registers(modbusHandler_t *modH, uint8_t slave_addr, 
                              uint16_t start_reg, uint16_t num_regs, uint16_t *data) {
    modbus_t multi_read_cmd = {
        .u8id = slave_addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = start_reg,
        .u16CoilsNo = num_regs,
        .u16reg = NULL
    };

    uint32_t result = ModbusQueryV2(modH, multi_read_cmd);
    if(result == OP_OK_QUERY) {
        for(int i=0; i<num_regs; i++) {
            data[i] = modH->u16regs[i];
        }
        return MODBUS_Q_OK;
    }
    return MODBUS_Q_ERR;
}

// 示例3: 设置从机地址
int16_t set_slave_address(modbusHandler_t *modH, uint8_t old_addr, uint8_t new_addr) {
    // 使用静态变量避免指针生命周期问题
    static uint16_t addr_data;
    addr_data = new_addr;

    if(new_addr < 1 || new_addr > 254) {
        return -1;
    }
    modbus_t addr_write_cmd = {
        .u8id = old_addr,
        .u8fct = MB_FC_WRITE_REGISTER,
        .u16RegAdd = REG_ADDRESS, // 从机地址寄存器
        .u16CoilsNo = 1,
        .u16reg = &addr_data
    };

    uint32_t result = ModbusQueryV2(modH, addr_write_cmd);
    return (result == OP_OK_QUERY) ? MODBUS_Q_OK : MODBUS_Q_ERR;
}

// 示例4: 设置波特率
int16_t set_baudrate(modbusHandler_t *modH, uint8_t slave_addr, uint8_t baud_level) {
    // 使用静态变量避免指针生命周期问题
    static uint16_t baud_data;
    baud_data = baud_level;

    modbus_t baud_write_cmd = {
        .u8id = slave_addr,
        .u8fct = MB_FC_WRITE_REGISTER,
        .u16RegAdd = REG_BAUDRATE, // 波特率寄存器
        .u16CoilsNo = 1,
        .u16reg = &baud_data
    };

    uint32_t result = ModbusQueryV2(modH, baud_write_cmd);
    return (result == OP_OK_QUERY) ? MODBUS_Q_OK : MODBUS_Q_ERR;
}

/* 发波控制 */
uint8_t US_SetBurstMode(modbusHandler_t *modH, uint16_t master_mask)
{
    // 使用静态变量避免指针生命周期问题
    static uint16_t burst_data;
    burst_data = master_mask;

    modbus_t burst_broadcast_cmd = {
        .u8id = 0xFF, // 广播地址
        .u8fct = MB_FC_WRITE_REGISTER,
        .u16RegAdd = REG_BURST_CTRL, // 突发控制寄存器
        .u16CoilsNo = 1,
        .u16reg = &burst_data      //主机序号地址， 低8位置位则为主机发波， 旁边从机默认侦听
    };
    // 广播命令使用专用函数，不等待响应
    ModbusQueryBroadcast(modH, burst_broadcast_cmd);
    return MODBUS_Q_OK;
}


// 示例5: 非法操作处理(演示)
void handle_invalid_operation(modbusHandler_t *modH) {
    // 这里演示如何处理非法操作响应
    if(modH->i8lastError == ERR_EXCEPTION) {
//        uint8_t exception_code = modH->u8Buffer[2];
        // 根据异常代码处理...
    }
}


/* 读取完整回波信息 */
uint8_t US_ReadEchoInfo(modbusHandler_t *modH, uint8_t addr, SensorEchoInfoTypedef *echo)
{
    // 使用静态变量避免指针生命周期问题
    static uint16_t echo_data[16];
    uint8_t retry_count = 0;
    uint8_t result = MODBUS_Q_ERR;

    modbus_t echo_read_cmd = {
        .u8id = addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = REG_ECHO_RINGING_TIME, // 从余震时间寄存器开始读取
        .u16CoilsNo = 16,
        .u16reg = echo_data
    };

    // 针对传感器1和3的特殊处理
    uint8_t sensor_id = (addr >= 1 && addr <= 8) ? (addr - 1) : 0;
    uint8_t is_problematic_sensor = (sensor_id == 1 || sensor_id == 3); // 传感器1和3

    // 实现重试机制，对问题传感器增加重试次数和延迟
    uint8_t max_retries = is_problematic_sensor ? 5 : 3; // 问题传感器更多重试
    uint8_t retry_delay = is_problematic_sensor ? 20 : 10; // 问题传感器更长延迟

    while(retry_count < max_retries) {
        // 对问题传感器添加额外的预延迟
        if(is_problematic_sensor && retry_count == 0) {
            HAL_Delay(30); // 首次读取前额外延迟
        }

        // 对问题传感器打印查询命令信息
        if(is_problematic_sensor) {
            DEBUG_MODBUS("QUERY[Sensor%d]: addr=%d, func=0x%02X, start_reg=%d, reg_count=%d\r\n",
                         sensor_id, echo_read_cmd.u8id, echo_read_cmd.u8fct,
                         echo_read_cmd.u16RegAdd, echo_read_cmd.u16CoilsNo);
        }

        result = ModbusQueryV2(modH, echo_read_cmd);

        // 添加关键调试信息
        if(is_problematic_sensor) {
            DEBUG_MODBUS("Sensor%d(addr=%d) retry=%d, result=%s\r\n",
                         sensor_id, addr, retry_count,
                         (result == OP_OK_QUERY) ? "OK" : "FAIL");
        }

        if(result == OP_OK_QUERY) {
            // 通信成功，重置超时计数
            extern uint8_t Gu8ModbusTimeoutCnt[8]; // SENSOR_NUMBER (扩展到8)
            extern uint8_t Gu8ModbusRetryCnt[8];   // SENSOR_NUMBER (扩展到8)

            if(sensor_id < 8) { // SENSOR_NUMBER (扩展到8)
                Gu8ModbusTimeoutCnt[sensor_id] = 0;
                Gu8ModbusRetryCnt[sensor_id] = 0;
            }
            break;
        } else {
            retry_count++;
            // 对问题传感器使用更长的重试延迟
            HAL_Delay(retry_delay);
        }
    }

    // 如果重试失败，更新超时计数
    if(result != OP_OK_QUERY) {
        extern uint8_t Gu8ModbusTimeoutCnt[8]; // 扩展到8传感器
        uint8_t sensor_id = (addr >= 1 && addr <= 8) ? (addr - 1) : 0;
        if(sensor_id < 8) { // 扩展到8传感器
            Gu8ModbusTimeoutCnt[sensor_id]++;
        }
    }

    if(result == OP_OK_QUERY) {
        // 对问题传感器打印完整的原始回波数据
        if(is_problematic_sensor) {
            char raw_data_buffer[512] = {0};
            int offset = 0;
            offset += snprintf(raw_data_buffer + offset, sizeof(raw_data_buffer) - offset, "[Sensor%d]: ", sensor_id);
            for(uint8_t i = 0; i < 16; i++) {
                offset += snprintf(raw_data_buffer + offset, sizeof(raw_data_buffer) - offset, "reg[%d]=%d ", i, echo_data[i]);
            }
            DEBUG_MODBUS_RAW("%s\r\n", raw_data_buffer);
        }

        // 解析基本信息
        echo->u16RingingTime = echo_data[0];    // 余震时间
        echo->u8SampleFreq = echo_data[1] & 0xFF; // 采样频率
        echo->u8NoiseCnt = echo_data[2] & 0xFF;   // 噪声计数
        echo->u8SnsStaFlg = echo_data[3] & 0xFF;  // 状态标志

        // 解析多个回波数据 (最多3个回波)
        for(uint8_t j = 0; j < MAX_ECHO_READ && j < MAX_ECHO_CNT; j++) {
            uint16_t base_index = 3 + j * ECHO_REGS_PER_ECHO;

            echo->EchoInfo[j].u16EchoTime = echo_data[base_index];     // 回波时间
            echo->EchoInfo[j].u16EchoWidth = echo_data[base_index + 1]; // 回波宽度
            echo->EchoInfo[j].u8EchoVoltageLevel = echo_data[base_index + 2] & 0xFF; // 回波电压
            echo->EchoInfo[j].u8EchoConfidence = echo_data[base_index + 3] & 0xFF;   // 回波置信度
        }

        // 对问题传感器添加详细的解析后数据调试
        if(is_problematic_sensor) {
            DEBUG_MODBUS_PARSED("[Sensor%d]: RingTime=%d, Freq=%d, Noise=%d, Status=0x%02X\r\n",
                                sensor_id, echo->u16RingingTime, echo->u8SampleFreq,
                                echo->u8NoiseCnt, echo->u8SnsStaFlg);

            char echo_data_buffer[512] = {0};
            int offset = 0;
            offset += snprintf(echo_data_buffer + offset, sizeof(echo_data_buffer) - offset, "[Sensor%d]: ", sensor_id);
            for(uint8_t j = 0; j < MAX_ECHO_READ && j < MAX_ECHO_CNT; j++) {
                offset += snprintf(echo_data_buffer + offset, sizeof(echo_data_buffer) - offset,
                                  "Echo[%d]: Time=%d, Width=%d, Volt=%d, Conf=%d | ",
                                  j, echo->EchoInfo[j].u16EchoTime, echo->EchoInfo[j].u16EchoWidth,
                                  echo->EchoInfo[j].u8EchoVoltageLevel, echo->EchoInfo[j].u8EchoConfidence);
            }
            DEBUG_MODBUS_ECHO("%s\r\n", echo_data_buffer);

            // 数据有效性检查
            if(echo->EchoInfo[0].u16EchoTime == 0xFFFF || echo->EchoInfo[0].u16EchoTime == 0) {
                DEBUG_MODBUS_WARNING("Sensor%d invalid EchoTime=%d\r\n",
                                     sensor_id, echo->EchoInfo[0].u16EchoTime);
            }
        }

        // 清空剩余的回波数据
        for(uint8_t j = MAX_ECHO_READ; j < MAX_ECHO_CNT; j++) {
            echo->EchoInfo[j].u16EchoTime = INVALID_ECHO_TIME;
            echo->EchoInfo[j].u16EchoWidth = 0;
            echo->EchoInfo[j].u8EchoVoltageLevel = 0;
            echo->EchoInfo[j].u8EchoConfidence = 0;
        }

        // 保存有效数据用于故障时的降级处理
        UpdateLastValidData(echo, addr);

        return MODBUS_Q_OK;
    } else {
        // 通信失败时的降级处理
        ModbusCommunicationFallback(echo, addr);
        return MODBUS_Q_ERR;
    }
}

/* 读取单个回波数据 */
uint8_t US_ReadSingleEcho(modbusHandler_t *modH, uint8_t addr, uint8_t echo_index, EchoInfoTypedef *echo_info)
{
    if(echo_index >= MAX_ECHO_READ) {
        return MODBUS_Q_ERR;
    }

    // 使用静态变量避免指针生命周期问题
    static uint16_t echo_data[ECHO_REGS_PER_ECHO];
    uint16_t reg_addr = REG_ECHO_DATA_START + echo_index * ECHO_REGS_PER_ECHO;

    modbus_t single_echo_cmd = {
        .u8id = addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = reg_addr,
        .u16CoilsNo = ECHO_REGS_PER_ECHO,
        .u16reg = echo_data
    };

    if(ModbusQueryV2(modH, single_echo_cmd) == OP_OK_QUERY) {
        echo_info->u16EchoTime = echo_data[0];
        echo_info->u16EchoWidth = echo_data[1];
        echo_info->u8EchoVoltageLevel = echo_data[2] & 0xFF;
        echo_info->u8EchoConfidence = echo_data[3] & 0xFF;
        return MODBUS_Q_OK;
    }

    return MODBUS_Q_ERR;
}

/* 读取温度值(℃) */
uint8_t US_ReadTemperature(modbusHandler_t *modH, uint8_t addr, float *temp)
{
    // 使用静态变量避免指针生命周期问题
    static uint16_t temp_data;

    modbus_t temp_read_cmd = {
        .u8id = addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = 0x0021, // 温度寄存器
        .u16CoilsNo = 1,
        .u16reg = &temp_data
    };

    uint32_t result = ModbusQueryV2(modH, temp_read_cmd);
    if(result == OP_OK_QUERY) {
        *temp = (float)modH->u16regs[0] / 10.0f; // 协议规定温度值需除以10
        return MODBUS_Q_OK;
    }

    return MODBUS_Q_ERR;
}

/* 电压转换函数 */
static float ConvertVoltage(uint16_t raw)
{
    // 根据协议文档的电压参数转换
    if(raw >= VOLTAGE_180) return 18.0f;
    if(raw >= VOLTAGE_175) return 17.5f;
    if(raw >= VOLTAGE_165) return 16.5f;
    if(raw >= VOLTAGE_162) return 16.2f;
    if(raw >= VOLTAGE_88) return 8.8f;
    if(raw >= VOLTAGE_85) return 8.5f;
    if(raw >= VOLTAGE_75) return 7.5f;
    return 7.0f;
}

/* 读取电压值(V) */
uint8_t US_ReadVoltage(modbusHandler_t *modH, uint8_t addr, float *voltage, uint8_t retry)
{
    // 使用静态变量避免指针生命周期问题
    static uint16_t voltage_data;

    modbus_t voltage_read_cmd = {
        .u8id = addr,
        .u8fct = MB_FC_READ_REGISTERS,
        .u16RegAdd = 0x0022, // 电压寄存器
        .u16CoilsNo = 1,
        .u16reg = &voltage_data
    };

    if(ModbusQueryV2(modH, voltage_read_cmd) == OP_OK_QUERY) {
        *voltage = ConvertVoltage(modH->u16regs[0]); // 使用modH->u16regs而不是局部变量
        return MODBUS_Q_OK;
    }

    return MODBUS_Q_ERR;
}

/******************************************************************************
 * 函数名称: ModbusCommunicationFallback
 * 功能描述: Modbus通信故障时的降级处理
 * 输入参数: echo - 回波数据结构, addr - 设备地址
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 当Modbus通信失败时，使用默认值或上次有效数据
 *******************************************************************************/
void ModbusCommunicationFallback(SensorEchoInfoTypedef *echo, uint8_t addr)
{
    static SensorEchoInfoTypedef last_valid_data[8] = {0}; // 保存上次有效数据 (扩展到8)
    uint8_t sensor_id = (addr >= 1 && addr <= 8) ? (addr - 1) : 0; // 支持1-8

    if(sensor_id < 8) { // 扩展到8传感器
        // 使用上次有效数据
        if(last_valid_data[sensor_id].u16RingingTime != 0) {
            *echo = last_valid_data[sensor_id];
        } else {
            // 如果没有历史数据，使用默认值
            echo->u16RingingTime = 1000;  // 默认余震时间
            echo->u8SampleFreq = 40;      // 默认频率
            echo->u8NoiseCnt = 0;         // 默认噪声计数
            echo->u8SnsStaFlg = 0;        // 默认状态

            // 清空回波数据
            for(uint8_t i = 0; i < MAX_ECHO_CNT; i++) {
                echo->EchoInfo[i].u16EchoTime = INVALID_ECHO_TIME;
                echo->EchoInfo[i].u16EchoWidth = 0;
                echo->EchoInfo[i].u8EchoVoltageLevel = 0;
                echo->EchoInfo[i].u8EchoConfidence = 0;
            }
        }

        // 标记传感器通信错误
        extern uint8_t Gu8SensorComErrFlag;
        Gu8SensorComErrFlag |= (1 << sensor_id);
    }
}

/******************************************************************************
 * 函数名称: UpdateLastValidData
 * 功能描述: 更新上次有效数据
 * 输入参数: echo - 回波数据结构, addr - 设备地址
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 保存有效的通信数据用于故障时的降级处理
 *******************************************************************************/
void UpdateLastValidData(const SensorEchoInfoTypedef *echo, uint8_t addr)
{
    static SensorEchoInfoTypedef last_valid_data[8] = {0}; // 扩展到8传感器
    uint8_t sensor_id = (addr >= 1 && addr <= 8) ? (addr - 1) : 0; // 支持1-8

    if(sensor_id < 8 && echo != NULL) { // 扩展到8传感器
        last_valid_data[sensor_id] = *echo;
    }
}
