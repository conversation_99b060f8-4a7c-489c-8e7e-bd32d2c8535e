# 距离计算调试分析

## 🚨 当前问题

所有`Gu8SysDistance`数组值都是255 (0xFF = NO_OBJECT)，表明距离计算算法没有正确更新数组。

## 🔍 问题定位

### **数据流程分析**
```
Modbus读取 → UltrasonicAlg_ProcessData → CalcSnsObjDistance → UpdateSnsDistance → Gu8SysDistance
```

### **可能的问题点**

#### **1. Modbus数据读取问题**
- **检查点**: `US_ReadEchoInfo()` 是否成功
- **症状**: 如果读取失败，不会进入后续处理
- **调试**: 添加了Modbus读取状态打印

#### **2. 回波时间数据问题**
- **检查点**: `sensor_data.EchoInfo[0].u16EchoTime` 是否有效
- **症状**: 如果回波时间为0或无效值，距离计算失败
- **调试**: 添加了原始回波数据打印

#### **3. 距离计算算法问题**
- **检查点**: `CalcTriangleDistance()` 计算结果
- **症状**: 计算结果可能超出有效范围
- **调试**: 添加了距离计算过程打印

#### **4. 滤波算法过于严格**
- **检查点**: `UpdateSnsDistance()` 滤波逻辑
- **症状**: 复杂的滤波条件可能阻止距离更新
- **调试**: 添加了滤波过程详细打印

## ✅ 添加的调试功能

### **1. 简单距离测试**
```c
void Robot_SimpleDistanceTest(void)
{
    // 绕过复杂滤波，直接计算距离
    for(uint8_t modbus_id = 1; modbus_id <= 4; modbus_id++) {
        SensorEchoInfoTypedef sensor_data;
        if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
            uint16_t echo_time = sensor_data.EchoInfo[0].u16EchoTime;
            uint16_t simple_distance = (echo_time > 0 && echo_time < 20000) ? echo_time / 58 : 0;
            printf("Sensor%d: EchoTime=%d, SimpleDistance=%dcm\r\n",
                   modbus_id-1, echo_time, simple_distance);
        }
    }
}
```

### **2. Modbus读取状态调试**
```c
if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
    printf("DEBUG: Sensor%d Modbus read OK - EchoTime[0]=%d, RingingTime=%d, SampleFreq=%d\r\n",
           sensor_id, sensor_data.EchoInfo[0].u16EchoTime, 
           sensor_data.u16RingingTime, sensor_data.u8SampleFreq);
}
```

### **3. 距离计算过程调试**
```c
printf("DEBUG: CalcSnsObjDistance[%d] - Lu8SnsDistanceMin=%d, before update Gu8SysDistance=%d\r\n",
       LenuSensorID, Lu8SnsDistanceMin, Gu8SysDistance[LenuSensorID]);

UpdateSnsDistance(Lu8SnsDistanceMin, LenuSensorID);

printf("DEBUG: CalcSnsObjDistance[%d] - after update Gu8SysDistance=%d\r\n",
       LenuSensorID, Gu8SysDistance[LenuSensorID]);
```

### **4. 滤波算法调试**
```c
void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID)
{
    printf("DEBUG: UpdateSnsDistance[%d] - input=%d, current=%d\r\n",
           LenuSensorID, Lu8Distance, Gu8SysDistance[LenuSensorID]);
    
    // 距离增大时的更新
    if (Gu8SnsDisUpdateCnt[LenuSensorID][1] > OBJ_FAR_CNT) {
        printf("DEBUG: UpdateSnsDistance[%d] - FAR update: %d -> %d\r\n",
               LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
    }
    
    // 距离减小时的更新
    if (Gu8SnsDisUpdateCnt[LenuSensorID][2] > Gu8CompareTime + ret) {
        printf("DEBUG: UpdateSnsDistance[%d] - NEAR update: %d -> %d\r\n",
               LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
    }
    
    // 噪声抑制
    if(stdevpx.mean > 8) {
        printf("DEBUG: UpdateSnsDistance[%d] - NOISE suppression: mean=%d > 8\r\n",
               LenuSensorID, (int)stdevpx.mean);
    }
}
```

## 📊 预期调试输出

### **正常情况下应该看到**
```
=== Simple Distance Test ===
Sensor0: EchoTime=6960, SimpleDistance=120cm
Sensor1: EchoTime=8700, SimpleDistance=150cm
Sensor2: EchoTime=11600, SimpleDistance=200cm
Sensor3: EchoTime=10440, SimpleDistance=180cm
============================

DEBUG: Sensor0 Modbus read OK - EchoTime[0]=6960, RingingTime=1200, SampleFreq=40
DEBUG: CalcSnsObjDistance[0] - Lu8SnsDistanceMin=120, before update Gu8SysDistance=255
DEBUG: UpdateSnsDistance[0] - input=120, current=255
DEBUG: UpdateSnsDistance[0] - NEAR update: 255 -> 120
DEBUG: CalcSnsObjDistance[0] - after update Gu8SysDistance=120
```

### **问题情况下可能看到**
```
=== Simple Distance Test ===
Sensor0: EchoTime=0, SimpleDistance=0cm
Sensor1: Read FAILED
Sensor2: EchoTime=65535, SimpleDistance=0cm
Sensor3: EchoTime=100, SimpleDistance=1cm
============================

DEBUG: CalcSnsObjDistance[0] - Lu8SnsDistanceMin=255, before update Gu8SysDistance=255
DEBUG: UpdateSnsDistance[0] - input=255, current=255
DEBUG: UpdateSnsDistance[0] - NOISE suppression: mean=15 > 8
DEBUG: CalcSnsObjDistance[0] - after update Gu8SysDistance=255
```

## 🎯 调试策略

### **第一步：检查Modbus通信**
- 查看简单距离测试的输出
- 确认是否能读取到有效的回波时间数据
- 如果读取失败，问题在Modbus通信层

### **第二步：检查距离计算**
- 查看原始回波时间是否合理 (应该在100-20000范围内)
- 确认简单距离计算是否正确
- 如果简单计算正确但复杂算法失败，问题在滤波逻辑

### **第三步：检查滤波逻辑**
- 查看`UpdateSnsDistance`的调试输出
- 确认是否因为滤波条件过严而阻止更新
- 检查噪声抑制是否过于敏感

### **第四步：临时绕过滤波**
如果确认是滤波问题，可以临时简化滤波逻辑：
```c
void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID)
{
    // 临时简化：直接更新，绕过复杂滤波
    if(Lu8Distance != NO_OBJECT && Lu8Distance > 0 && Lu8Distance < 200) {
        Gu8SysDistance[LenuSensorID] = Lu8Distance;
        printf("DEBUG: Direct update Gu8SysDistance[%d] = %d\r\n", LenuSensorID, Lu8Distance);
    }
}
```

## 📋 下一步行动

1. **运行调试版本**：查看详细的调试输出
2. **分析数据流**：确定在哪一步数据丢失或变为无效
3. **针对性修复**：根据调试结果修复具体问题
4. **验证修复**：确认距离数据能正确更新到`Gu8SysDistance`数组

通过这些调试信息，我们应该能够准确定位为什么所有距离值都是255 (NO_OBJECT)的根本原因。
