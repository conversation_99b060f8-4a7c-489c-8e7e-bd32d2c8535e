#ifndef ECHO_PROCESSING_H
#define ECHO_PROCESSING_H

//#include "ultrasonic_alg.h"
#include <stdint.h>
//#include "modbus_interface.h"

#define FINAL_DISTANCE_LIMIT 20u   // 最终三角计算距离限制，也用于盲区距离限制判断
#define NORMAL_VOLTAGE_THRESHOLD 88u                               // (默认90,建议正负5)电压阈值，用于区分碎石和路沿障碍物
#define MIN_VOLTAGE_THRESHOLD (NORMAL_VOLTAGE_THRESHOLD * 0.6)     // (默认0.6,建议不变)用于碎石在强回波距离段下---过滤的最小电压阈值
#define VOLTAGE_THRESHOLD_AVERAGE (NORMAL_VOLTAGE_THRESHOLD * 1.2) // (默认1.2,建议正负0.1)用于区分是处于碎石还是正常障碍物状态下的电压阈值的平均和(碎石状态下一般情况小于200)

#define NEAR_WARN_DIS 30u    // 最近段报警距离，用于分段报警
#define MIDDLE_WARN_DIS 50u  // 中间段报警距离，用于分段报警
#define MAX_ZONE_NUM 10u     // 报警分段数

// #define TWICE_DIS_VAL_DIFF_TIME (58 * 35) /**< 12Km/h  3.33m/s  0.12秒移动了 39 0.1秒移动了33 */
// #define TWICE_DIS_VAL_DIFF_TIME5KM (58 * 17) /**<5km/h = 1.3889m/s 0.12秒移动了 17 */
#define TWICE_DIS_VAL_DIFF_TIME (58 * 200)   /**< 放宽到200cm变化阈值，适合静态障碍物检测 */
#define TWICE_DIS_VAL_DIFF_TIME5KM (58 * 100) /**< 放宽到100cm变化阈值，适合低速静态检测 */
#define TWICE_DIS_VAL_DIFF_NOISE (58 * 3)    /**噪音环境只检测静止障碍物 跳动距离3CM */
#define DOUBLE_ECHO_TIME (58 * 100)          // 回波时间在此范围内判断余震变化
#define INVALID_ECHO_TIME (uint16_t)0xffff     // 回波无效的时候回波默认初始化时间值

#define OBJ_FAR_CNT 2   // 障碍物远离是否更新的最大判断次数
#define OBJ_NEAR_CNT 1  // 障碍物靠近是否更新的最大判断次数

#define NO_OBJECT 255   // 无有效障碍物的时候默认初始化距离值(马恒达项目客户要求定义254)

#define NO_LISTEN_MAX_DIS 80u     // 只有主发的时候最大探测距离CM

#define MAX_ECHO_CNT 10 /**< 最多3个有效回波 */

#define RING_MIN 500    // 余震判断最小值
#define RING_MAX 1600   // 余震判断最大值
#define RING_ERR_CONFIRM 5u  // 余震错误确定次数
#define RING_CHANGE_THR 50u  // 余震变化最小判断阈值
#define RING_ADD_LEVEL1 150u // 余震变化次小判断阈值
#define RING_ADD_LEVEL2 300u // 余震变化次中判断阈值

#define RING_CHANGE_CNT 10u // 余震变化次数计数最大值
#define RING_STABLE_CNT 50u // 余震稳定次数计数最大值


#define CORNER_CORNER 0xff // 边角探头到边角探头的直线距离(4探头及以下系统默认不考虑)
#define CENTER_CENTER 71u // 中间探头到中间探头的直线距离

#define CENTER_RAR_WARN_DIS 150u // cm中间探头探测障碍物的Y轴最大距离
#define CORNER_FAR_WARN_DIS 100u // cm边角探头探测障碍物的Y轴最大距离
#define CENTER_SEN_X_MAX 60u     // cm中间探头探测障碍物的X轴最大距离
#define CORNER_SEN_X_MAX 50u     // cm边角探头探测障碍物的X轴最大距离
#define MASTER_DIATANCE_COORD 45u     // cm只有主发时候的比例分割线60 - 45 -35   、、这个数值的确定和安装间距有关系，从没有监听的格子看数据

#define PLAT_COR_CEN_DIS (uint8_t)41 //边角探头到中间探头的直线距离
#define PLAT_COR_COSB (uint8_t)98   //边角和中间探头Y轴偏移的夹角cos
#define PLAT_COR_SINB (uint8_t)21   // 边角和中间探头Y轴偏移的夹角sin

#define SENSOR_LISTEN_NUMBER1 3

// 传感器错误状态定义
typedef enum {
    ERR_STA_NULL = 0,             // 无错误
    ERR_STA_COM,                  // 通信故障
    ERR_STA_BLOCK,                // 探头被遮挡或覆盖
    ERR_STA_OVER_VOLT,            // 过压
    ERR_STA_LOW_VOLT,             // 低压
    ERR_STA_IN_BLIND,             // 在盲区
    ERR_STA_NOISE,                // 噪音异常
    ERR_STA_OVER_TEMPERATION,     // 过温
    ERR_STA_RING,                 // 余震故障
    ERR_STA_COM_GND_VCC,          // 通信接地、电源故障
    ERR_STA_FRE,                  // 频率异常
    ERR_STA_SW,                   // 软件故障
    ERR_STA_HW                    // 硬件故障
} tenu_SensorErrorTypedef;

// 传感器状态标志位定义
#define SNS_STA_BLOCK_FLAG      (1 << 0)  // 阻塞标志
#define SNS_STA_OVER_TEMP_FLAG  (1 << 3)  // 过温标志
#define SNS_STA_FREQ_ERR_FLAG   (1 << 4)  // 频率异常标志

// 错误检测相关常量
#define NOISE_CNT_MAX           15         // 最大噪声计数
#define NOISE_COMPARE_TIME      3          // 噪声比较时间
#define BUTSR_FREQUENCY_KHZ     40         // 标准频率(kHz)
#define FREQ_ERR_CONFIRM_CNT    10         // 频率错误确认次数
#define TEMP_ERR_CONFIRM_CNT    10         // 温度错误确认次数
#define MAX_TEMP_THRESHOLD      120        // 最大温度阈值(°C)
#define MODBUS_TIMEOUT_CNT      5          // Modbus通信超时计数阈值
#define MODBUS_RETRY_MAX        3          // Modbus重试最大次数

// 位操作宏定义
#define US_SET_BIT(reg, bit)       ((reg) |= (1 << (bit)))
#define US_CLR_BIT(reg, bit)       ((reg) &= ~(1 << (bit)))
#define US_GET_BIT(reg, bit)       (((reg) >> (bit)) & 1)
#define ABS(a, b)               ((a) > (b) ? ((a) - (b)) : ((b) - (a)))

typedef struct
{
    uint16_t u16EchoTime;
    uint16_t u16EchoWidth;
    uint8_t u8EchoVoltageLevel;
    uint8_t u8EchoConfidence;
} EchoInfoTypedef;


typedef struct
{
		uint16_t uTemperature;
    uint16_t u16OriginRinging; // DSP测量出余震时间
    uint16_t u16RingingTime;   // EDI根据发波第一个回波计算出的余震时间
    uint8_t u8SampleFreq;      // 计算出的探头发波频率(也就是探芯的频率)
    uint8_t u8NoiseCnt;
    uint8_t u8SnsStaFlg;
    EchoInfoTypedef EchoInfo[MAX_ECHO_CNT];
} SensorEchoInfoTypedef;


typedef enum
{
    SENSOR_MASTER_BURST = 0,
    SENSOR_LISTEN_RIGHT = 1,
    SENSOR_LISTEN_LEFT = 2,
    SENSOR_LISTEN_NUMBER = 3,
    SENSOR_IDLE = 0xff,
}tenu_SensorListenType;

typedef struct
{
    uint16_t u16RingingTime;
    uint16_t u16EchoTime[SENSOR_LISTEN_NUMBER];
    uint16_t u16EchoWidth[SENSOR_LISTEN_NUMBER];
    uint8_t u8EchoVoltageLevel[SENSOR_LISTEN_NUMBER];
    uint8_t u8EchoConfidence[SENSOR_LISTEN_NUMBER];
    uint8_t u8NoiseCnt[SENSOR_LISTEN_NUMBER];//噪音个数
    uint8_t u8SampleFreq;
    uint8_t u8SnsStaFlg;
    uint8_t OutSwVersion;
    uint8_t SnsHardWareID;
} SingleSensorObjectInfo;


typedef enum
{
    // 保持原有4传感器兼容性
    SENSOR_ROL = 0,
    SENSOR_RCL,
    SENSOR_RCR,
    SENSOR_ROR,

    // 扩展到8传感器 - 机器人底盘布局
    SENSOR_F1 = 0,    // 前左 (与ROL相同)
    SENSOR_F2 = 1,    // 前右 (与RCL相同)
    SENSOR_R1 = 2,    // 右前 (与RCR相同)
    SENSOR_R2 = 3,    // 右后 (与ROR相同)
    SENSOR_B1 = 4,    // 后右 (新增)
    SENSOR_B2 = 5,    // 后左 (新增)
    SENSOR_L1 = 6,    // 左后 (新增)
    SENSOR_L2 = 7,    // 左前 (新增)

    SENSOR_NUMBER = 8,
    SENSOR_MASTER = SENSOR_RCL,
    SENSOR_All_Listen = 0xAA, // 所有探头侦听，作为右侦听来进行噪音判断；
    SENSOR_NONE = 0xff
} tenu_SensorIDTypedef;


typedef struct
{
    int u16OriginDis[SENSOR_NUMBER][SENSOR_LISTEN_NUMBER1];
    int s16ObjXcoord[SENSOR_NUMBER];
    uint16_t s16ObjYcoord[SENSOR_NUMBER];
    uint8_t u8ObjListenFlag[SENSOR_NUMBER];
    uint8_t cNosicCnt;
} SystemObjInfoTypedef;


#define STDEVP_LEN 32
#define STDEVP_16_LEN 8
typedef struct
{
    uint16_t mean;              // 平均值
    uint16_t variance;          // 方差
    uint16_t buffer[STDEVP_16_LEN]; // 循环缓冲区
    int head;                   // 缓冲区头索引
    int full;                   // 缓冲区是否已满
} Statistic_16;

typedef struct
{
    uint8_t mean;               // 平均值
    uint8_t variance;           // 方差
    uint8_t buffer[STDEVP_LEN]; // 循环缓冲区
    int head;                    // 缓冲区头索引
    int full;                    // 缓冲区是否已满
} Statistic;

extern SensorEchoInfoTypedef GstrSnsEchoInfo;
extern SystemObjInfoTypedef GstrSystemObjInfo;
extern SingleSensorObjectInfo GstrSensorObjInfo[SENSOR_NUMBER], GstrSensorObjInfoBkp[SENSOR_NUMBER];
extern uint8_t Gu8SensorErrSta[SENSOR_NUMBER], Gu8SysDistance[SENSOR_NUMBER], Gu8SysWarningZone[SENSOR_NUMBER];
extern uint8_t Gu8SensorRingErrCnt[SENSOR_NUMBER], Gu8SnsRingAddFlag[SENSOR_NUMBER];
extern Statistic stdevpx;

// 新增的错误状态管理变量
extern uint8_t Gu8SensorComErrFlag;              // 传感器通信错误标志
extern uint8_t Gu8InblindErrFlag;                // 盲区错误标志
extern uint8_t Gu8SnsRingAddCnt[SENSOR_NUMBER];  // 余震增加计数
extern uint8_t Gu8CompareTime;                   // 动态比较时间
extern uint8_t Gu8SpeedData;                     // 车速数据
extern uint8_t Gu8ModbusTimeoutCnt[SENSOR_NUMBER]; // Modbus超时计数
extern uint8_t Gu8ModbusRetryCnt[SENSOR_NUMBER];   // Modbus重试计数


void UltrasonicAlg_Init(void);
void UltrasonicAlg_ProcessData(SensorEchoInfoTypedef* data, tenu_SensorIDTypedef sensor_id);

void CalcSnsObjDistance(tenu_SensorIDTypedef LenuSensorID);
void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID);
uint8_t GetCurrentNoiseInfo(void);
void Ultrasonic_ProcessData(SensorEchoInfoTypedef *echo, uint16_t burst_ctrl_id);

// 传感器错误状态管理函数
int SensorErrorCheck(tenu_SensorIDTypedef sensor_id);
int SnsBlockageErrorCheck(tenu_SensorIDTypedef sensor_id);
int SnsOverTemperationErrorCheck(tenu_SensorIDTypedef sensor_id);
int SnsFrequencyErrorCheck(tenu_SensorIDTypedef sensor_id);
int SnsRingErrorCheck(tenu_SensorIDTypedef sensor_id);
int SnsModbusComErrorCheck(tenu_SensorIDTypedef sensor_id);
void UpdateDynamicCompareTime(void);
void CalcWarningZone(tenu_SensorIDTypedef LenuSensorID, uint8_t Lu8Distance);

// 系统警告信息管理函数
void InitSystemWarningInfo(void);
void UpdateSystemWarningInfo(void);
uint8_t GetSensorMaxDistance(tenu_SensorIDTypedef sensor_id);
uint8_t GetWarningLevel(tenu_SensorIDTypedef sensor_id, uint8_t distance);
void ProcessSystemWarning(void);


#endif
