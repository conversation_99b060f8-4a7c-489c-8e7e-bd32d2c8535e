/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2021 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"
#include "modbus.h"
#include "modbus_interface.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "echo_processing.h"
#include <stdio.h>
#include "semphr.h"
#include "algorithm.h"
#include "robot_ultrasonic.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
extern modbusHandler_t ModbusH;
extern modbusHandler_t ModbusH2;
modbus_t telegram[2];
/* USER CODE END Variables */
/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};
/* Definitions for myTaskMaster */
osThreadId_t myTaskMasterHandle;
const osThreadAttr_t myTaskMaster_attributes = {
  .name = "myTaskMaster",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityLow,
};
/* Definitions for TaskmyTaskSlave */
osThreadId_t TaskmyTaskSlaveHandle;
const osThreadAttr_t TaskmyTaskSlave_attributes = {
  .name = "TaskmyTaskSlave",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityLow,
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void *argument);
void StartTaskMaster(void *argument);
void StartTasSlave(void *argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of defaultTask */
  defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);

  /* creation of myTaskMaster */
  myTaskMasterHandle = osThreadNew(StartTaskMaster, NULL, &myTaskMaster_attributes);

  /* creation of TaskmyTaskSlave */
//  TaskmyTaskSlaveHandle = osThreadNew(StartTasSlave, NULL, &TaskmyTaskSlave_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void *argument)
{
  /* USER CODE BEGIN StartDefaultTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartDefaultTask */
}

/**
 * @brief 传统超声波测距任务 (兼容模式)
 * @param robot_mode 运行模式：1=单传感器模式，2=方向模式
 */
void Traditional_UltrasonicTask(uint8_t robot_mode)
{
    printf("Starting Traditional Ultrasonic Task (Mode: %d)\r\n", robot_mode);

    // 传统4传感器模式 (兼容性保持)
    uint16_t burst_patterns[] = {
        0x01,  // 探头1主发
        0x02,  // 探头2主发
        0x04,  // 探头3主发
        0x08,  // 探头4主发
        0x10,  // 探头5主发 (新增)
        0x20,  // 探头6主发 (新增)
        0x40,  // 探头7主发 (新增)
        0x80   // 探头8主发 (新增)
    };
    uint8_t max_patterns = (robot_mode == 1) ? 8 : 4; // 单传感器模式支持8个，否则4个
    uint8_t pattern_index = 0;
    uint8_t cycle_count = 0;

    SensorEchoInfoTypedef echo_data;

    /* Infinite loop */
    for(;;)
    {
        // 1. 执行多探头协同测量
        uint16_t current_pattern = burst_patterns[pattern_index];

        printf("Burst Pattern: 0x%02X (Sensor %d)\r\n", current_pattern, pattern_index + 1);

        // 2. 执行完整的发波-收波-处理流程
        Ultrasonic_ProcessData(&echo_data, current_pattern);

        // 3. 获取处理结果
        for(int i = 0; i < SENSOR_NUMBER; i++) {
            if(Gu8SysDistance[i] != NO_OBJECT) {
                printf("Sensor[%d]: Distance=%d mm, X=%d, Y=%d\r\n",
                       i, Gu8SysDistance[i] * 58,
                       GstrSystemObjInfo.s16ObjXcoord[i],
                       GstrSystemObjInfo.s16ObjYcoord[i]);

                // 更新Modbus数据缓冲区供其他任务使用
                if(i < sizeof(ModbusDATA2)/sizeof(ModbusDATA2[0])) {
                    ModbusDATA2[i] = Gu8SysDistance[i] * 58; // 转换为mm
                }
            }
        }

        // 4. 切换到下一个发波模式
        pattern_index = (pattern_index + 1) % max_patterns;

        // 5. 每完成一轮探头的测量，进行一次系统状态检查
        if(pattern_index == 0) {
            cycle_count++;
            if(cycle_count % 5 == 0) {
                printf("Cycle %d completed. Noise variance: %d\r\n",
                       cycle_count, GetCurrentNoiseInfo());
            }
        }

        // 6. 任务间隔 - 根据系统需求调整
        osDelay(50);
    }
}

/* USER CODE BEGIN Header_StartTaskMaster */
/**
* @brief Function implementing the myTaskMaster thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartTaskMaster */
void StartTaskMaster(void *argument)
{
  /* USER CODE BEGIN StartTaskMaster */
    // 初始化算法模块
    UltrasonicAlg_Init();

    // 机器人模式选择
    // 0=集成测试，1=单传感器模式，2=方向模式，3=机器人智能模式
    uint8_t robot_mode = 3; // 默认机器人智能模式

    printf("=== Robot Ultrasonic System Startup ===\r\n");
    printf("Selected Mode: %d\r\n", robot_mode);

    switch(robot_mode) {
        case 0:
            // 运行集成测试
            printf("Running Robot Integration Test...\r\n");
            extern void Robot_RunIntegrationTest(void);
            Robot_RunIntegrationTest();

            // 测试完成后切换到机器人模式
            printf("Integration test completed, switching to robot mode...\r\n");
            osDelay(2000);
            robot_mode = 3;
            // 继续执行到case 3

        case 3:
            // 启动机器人智能超声波系统
            printf("Starting Robot Ultrasonic System (8 sensors)...\r\n");
            Robot_UltrasonicTask(); // 这个函数包含无限循环，不会返回
            break;

        case 1:
        case 2:
            // 传统模式 (兼容性保持)
            printf("Starting Traditional Mode...\r\n");
            Traditional_UltrasonicTask(robot_mode);
            break;

        default:
            printf("Invalid robot mode: %d, using default robot mode\r\n", robot_mode);
            Robot_UltrasonicTask();
            break;
    }
  /* USER CODE END StartTaskMaster */
}

/* USER CODE BEGIN Header_StartTasSlave */
/**
* @brief Function implementing the TaskmyTaskSlave thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartTasSlave */
void StartTasSlave(void *argument)
{
  /* USER CODE BEGIN StartTasSlave */
  /* Infinite loop */
  for(;;)
  {

		xSemaphoreTake(ModbusH.ModBusSphrHandle , portMAX_DELAY);
		//HAL_GPIO_WritePin(LD2_GPIO_Port, LD2_Pin, ModbusH.u16regs[0] & 0x1);
		xSemaphoreGive(ModbusH.ModBusSphrHandle);

		osDelay(200);
  }
  /* USER CODE END StartTasSlave */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */

