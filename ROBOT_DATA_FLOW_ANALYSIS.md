# 机器人测试逻辑中GstrRobotDirectionInfo数据来源和计算算法分析

## 📊 数据流程概览

```
超声波回波 → Modbus读取 → 原始处理 → 距离计算 → 方向融合 → GstrRobotDirectionInfo
```

## 🔍 详细数据流程分析

### 1. **数据源头：超声波回波信息**

#### **原始数据结构**
```c
typedef struct {
    uint16_t u16EchoTime;           // 回波时间 (μs)
    uint16_t u16EchoWidth;          // 回波宽度 (μs)
    uint8_t u8EchoVoltageLevel;     // 回波电压等级
    uint8_t u8EchoConfidence;       // 回波置信度
} EchoInfoTypedef;

typedef struct {
    EchoInfoTypedef EchoInfo[MAX_ECHO_CNT];  // 多个回波信息
    uint16_t u16RingingTime;                 // 余震时间
    uint8_t u8SampleFreq;                    // 采样频率
    uint8_t u8SnsStaFlg;                     // 传感器状态标志
    uint16_t uTemperature;                   // 温度信息
    uint8_t u8NoiseCnt;                      // 噪声计数
} SensorEchoInfoTypedef;
```

### 2. **第一级处理：Modbus数据读取**

#### **数据获取流程**
```c
// 在 Robot_ProcessDirection() 中
for(uint8_t i = 0; i < 2; i++) {
    uint8_t sensor_id = direction_sensors[direction][i];
    uint8_t modbus_id = sensor_id + 1; // Modbus ID从1开始
    
    if(burst_pattern & (0x01 << sensor_id)) {
        SensorEchoInfoTypedef sensor_data;
        // 关键：从Modbus读取原始回波数据
        if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
            // 传递给算法处理
            UltrasonicAlg_ProcessData(&sensor_data, sensor_id);
        }
    }
}
```

### 3. **第二级处理：距离计算算法**

#### **核心算法：CalcSnsObjDistance()**
```c
void CalcSnsObjDistance(tenu_SensorIDTypedef sensor_id)
{
    // 1. 回波时间转距离
    for (i = 0; i < SENSOR_LISTEN_NUMBER; i++) {
        if (GstrSensorObjInfo[sensor_id].u16EchoTime[i] < 20000) {
            // 关键计算：时间转距离
            Lu8SnsDistance[i] = CalcTriangleDistance(
                GstrSensorObjInfo[sensor_id].u16EchoTime[i]
            );
        }
    }
    
    // 2. 距离有效性验证和滤波
    // 3. 更新系统距离 Gu8SysDistance[sensor_id]
}
```

#### **距离转换公式**
```c
uint16_t CalcTriangleDistance(uint16_t echo_time_us)
{
    // 简化公式：距离 = 回波时间 / 58
    // 原理：声速343m/s，往返距离，单位转换
    // 距离(cm) = 时间(μs) * 343(m/s) / 2 / 10000
    //          ≈ 时间(μs) / 58
    return echo_time_us / 58;
}
```

### 4. **第三级处理：距离滤波和更新**

#### **动态滤波算法：UpdateSnsDistance()**
```c
void UpdateSnsDistance(uint8_t new_distance, tenu_SensorIDTypedef sensor_id)
{
    // 1. 距离限制检查
    if (new_distance < FINAL_DISTANCE_LIMIT) {
        new_distance = FINAL_DISTANCE_LIMIT;
    }
    
    // 2. 动态滤波逻辑
    if (new_distance <= max_distance) {
        if (new_distance > Gu8SysDistance[sensor_id]) {
            // 距离增大：需要多次确认
            if (Gu8SnsDisUpdateCnt[sensor_id][1] > OBJ_FAR_CNT) {
                Gu8SysDistance[sensor_id] = new_distance;
            }
        } else {
            // 距离减小：快速响应
            if (Gu8SnsDisUpdateCnt[sensor_id][2] > Gu8CompareTime) {
                Gu8SysDistance[sensor_id] = new_distance;
            }
        }
    }
    
    // 3. 噪声抑制
    if(noise_level > threshold) {
        Gu8SysDistance[sensor_id] = NO_OBJECT;
    }
}
```

### 5. **第四级处理：机器人方向融合**

#### **方向传感器映射**
```c
// 每个方向的传感器映射
static const uint8_t direction_sensors[4][2] = {
    {SENSOR_F1, SENSOR_F2},  // 前方: 传感器0,1
    {SENSOR_R1, SENSOR_R2},  // 右侧: 传感器2,3
    {SENSOR_B1, SENSOR_B2},  // 后方: 传感器4,5
    {SENSOR_L1, SENSOR_L2}   // 左侧: 传感器6,7
};
```

#### **方向数据融合算法：Robot_DirectionDataFusion()**
```c
void Robot_DirectionDataFusion(uint8_t direction)
{
    // 1. 获取同方向两个传感器的距离
    uint8_t sensor1 = direction_sensors[direction][0];
    uint8_t sensor2 = direction_sensors[direction][1];
    
    uint16_t dist1 = Gu8SysDistance[sensor1] * 58; // 转换为mm
    uint16_t dist2 = Gu8SysDistance[sensor2] * 58;
    
    // 2. 数据有效性检查
    bool valid1 = (dist1 != 0xFFFF && dist1 > 0 && dist1 < 5000);
    bool valid2 = (dist2 != 0xFFFF && dist2 > 0 && dist2 < 5000);
    
    // 3. 融合策略
    uint16_t fused_distance;
    if(valid1 && valid2) {
        uint16_t diff = abs(dist1 - dist2);
        if(diff < 100) { // 差异<10cm
            fused_distance = (dist1 + dist2) / 2;  // 取平均值
        } else {
            fused_distance = min(dist1, dist2);    // 取较小值(保守)
        }
    } else if(valid1) {
        fused_distance = dist1;  // 只有传感器1有效
    } else if(valid2) {
        fused_distance = dist2;  // 只有传感器2有效
    } else {
        fused_distance = 0xFFFF; // 都无效
    }
    
    // 4. 更新方向信息
    GstrRobotDirectionInfo.u16Distance[direction] = fused_distance;
    GstrRobotDirectionInfo.u8Confidence[direction] = 
        Robot_CalculateConfidence(valid1, valid2, dist1, dist2);
}
```

### 6. **置信度计算算法**

#### **Robot_CalculateConfidence()**
```c
uint8_t Robot_CalculateConfidence(bool valid1, bool valid2, 
                                 uint16_t dist1, uint16_t dist2)
{
    if(!valid1 && !valid2) return 0;    // 都无效：0%
    if(valid1 && !valid2) return 60;    // 单传感器：60%
    if(!valid1 && valid2) return 60;    // 单传感器：60%
    
    // 双传感器有效：根据一致性评估
    uint16_t diff = abs(dist1 - dist2);
    if(diff < 50)  return 95;    // 差异<5cm：95%
    if(diff < 100) return 80;    // 差异<10cm：80%
    if(diff < 200) return 65;    // 差异<20cm：65%
    return 50;                   // 差异较大：50%
}
```

### 7. **警告等级计算**

#### **Robot_UpdateWarningSystem()**
```c
void Robot_UpdateWarningSystem(void)
{
    for(uint8_t dir = 0; dir < 4; dir++) {
        uint16_t distance = GstrRobotDirectionInfo.u16Distance[dir];
        
        if(distance > 2000) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_SAFE;
        } else if(distance > 1000) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CAUTION;
        } else if(distance > 500) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_DANGER;
        } else {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CRITICAL;
        }
    }
}
```

## 🎯 GstrRobotDirectionInfo结构体字段来源总结

### **u16Distance[4]** - 各方向最近距离(mm)
- **数据源**: `Gu8SysDistance[sensor_id] * 58`
- **算法**: 同方向双传感器融合 (平均值或最小值)
- **更新**: `Robot_DirectionDataFusion()`

### **u8Confidence[4]** - 各方向数据可信度(0-100%)
- **数据源**: 双传感器数据一致性分析
- **算法**: 基于距离差异的置信度评估
- **更新**: `Robot_CalculateConfidence()`

### **u8WarningLevel[4]** - 各方向警告等级(0-3)
- **数据源**: 融合后的距离值
- **算法**: 距离阈值分级 (2m/1m/0.5m)
- **更新**: `Robot_UpdateWarningSystem()`

### **u32LastUpdateTime[4]** - 最后更新时间(ms)
- **数据源**: `osKernelSysTick()`
- **算法**: 系统时钟直接记录
- **更新**: `Robot_ProcessDirection()`

### **u8SensorStatus[8]** - 各传感器状态
- **数据源**: Modbus通信状态
- **算法**: 通信成功/失败标记
- **更新**: `Robot_ProcessDirection()`

## 🔄 完整数据流程图

```
E52436传感器 → Modbus通信 → SensorEchoInfoTypedef
     ↓
UltrasonicAlg_ProcessData() → CalcSnsObjDistance()
     ↓
CalcTriangleDistance() → UpdateSnsDistance()
     ↓
Gu8SysDistance[8] → Robot_DirectionDataFusion()
     ↓
GstrRobotDirectionInfo.u16Distance[4]
     ↓
Robot_CalculateConfidence() → u8Confidence[4]
     ↓
Robot_UpdateWarningSystem() → u8WarningLevel[4]
```

## 📊 关键算法参数

- **距离转换**: `距离(cm) = 回波时间(μs) / 58`
- **单位转换**: `距离(mm) = 距离(cm) * 10`
- **融合阈值**: 差异<10cm取平均，否则取最小值
- **置信度**: 基于双传感器一致性，范围0-95%
- **警告阈值**: 2m(安全)/1m(注意)/0.5m(危险)/0.2m(紧急)

这就是机器人测试逻辑中`GstrRobotDirectionInfo`的完整数据来源和计算过程！
