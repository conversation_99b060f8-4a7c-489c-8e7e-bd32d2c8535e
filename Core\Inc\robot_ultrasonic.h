/**
 * @file robot_ultrasonic.h
 * @brief 机器人底盘8传感器超声波测距系统头文件
 * @date 2024-07-08
 */

#ifndef __ROBOT_ULTRASONIC_H__
#define __ROBOT_ULTRASONIC_H__

#include "echo_processing.h"
#include "modbus_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

// 机器人传感器数量定义
#define ROBOT_SENSOR_NUMBER     8
#define ROBOT_DIRECTION_NUMBER  4

/**
 * @brief 机器人传感器ID枚举
 */
typedef enum
{
    // 前方传感器 (Forward)
    ROBOT_SENSOR_F1 = 0,    // 前左
    ROBOT_SENSOR_F2 = 1,    // 前右
    
    // 右侧传感器 (Right)  
    ROBOT_SENSOR_R1 = 2,    // 右前
    ROBOT_SENSOR_R2 = 3,    // 右后
    
    // 后方传感器 (Backward)
    ROBOT_SENSOR_B1 = 4,    // 后右
    ROBOT_SENSOR_B2 = 5,    // 后左
    
    // 左侧传感器 (Left)
    ROBOT_SENSOR_L1 = 6,    // 左后
    ROBOT_SENSOR_L2 = 7,    // 左前
    
    ROBOT_SENSOR_NONE = 0xff
} tenu_RobotSensorIDTypedef;

/**
 * @brief 机器人方向枚举
 */
typedef enum
{
    ROBOT_DIRECTION_FORWARD = 0,   // 前方: F1, F2
    ROBOT_DIRECTION_RIGHT = 1,     // 右侧: R1, R2
    ROBOT_DIRECTION_BACKWARD = 2,  // 后方: B1, B2
    ROBOT_DIRECTION_LEFT = 3       // 左侧: L1, L2
} tenu_RobotDirectionTypedef;

/**
 * @brief 机器人运动状态枚举
 */
typedef enum {
    ROBOT_MOTION_FORWARD,    // 前进
    ROBOT_MOTION_BACKWARD,   // 后退
    ROBOT_MOTION_TURN_LEFT,  // 左转
    ROBOT_MOTION_TURN_RIGHT, // 右转
    ROBOT_MOTION_STOP        // 停止
} tenu_RobotMotionStateTypedef;

/**
 * @brief 警告等级枚举
 */
typedef enum {
    ROBOT_WARNING_SAFE = 0,      // 安全距离 (>2m)
    ROBOT_WARNING_CAUTION = 1,   // 注意距离 (1-2m)
    ROBOT_WARNING_DANGER = 2,    // 危险距离 (0.5-1m)
    ROBOT_WARNING_CRITICAL = 3   // 紧急距离 (<0.5m)
} tenu_RobotWarningLevelTypedef;

/**
 * @brief 机器人方向信息结构体
 */
typedef struct
{
    uint16_t u16Distance[ROBOT_DIRECTION_NUMBER];      // 各方向最近距离(mm)
    uint8_t u8Confidence[ROBOT_DIRECTION_NUMBER];     // 各方向数据可信度(0-100)
    uint8_t u8WarningLevel[ROBOT_DIRECTION_NUMBER];   // 各方向警告等级
    uint32_t u32LastUpdateTime[ROBOT_DIRECTION_NUMBER]; // 最后更新时间(ms)
    uint8_t u8SensorStatus[ROBOT_SENSOR_NUMBER];      // 各传感器状态
} RobotDirectionInfoTypedef;

/**
 * @brief 机器人配置结构体
 */
typedef struct
{
    uint16_t u16MinSafeDistance;    // 最小安全距离(mm)
    uint16_t u16MaxDetectDistance;  // 最大检测距离(mm)
    uint8_t u8UpdateFrequency;      // 更新频率(Hz)
    tenu_RobotMotionStateTypedef eMotionState; // 当前运动状态
    uint8_t u8EnableAdaptiveMode;   // 是否启用自适应模式
} RobotConfigTypedef;

/**
 * @brief 发波策略结构体
 */
typedef struct
{
    uint16_t u16BurstPattern;       // 发波模式
    uint8_t u8Priority;             // 优先级(1-10)
    uint16_t u16DelayMs;            // 延时时间(ms)
} RobotBurstStrategyTypedef;

// 全局变量声明
extern RobotDirectionInfoTypedef GstrRobotDirectionInfo;
extern RobotConfigTypedef GstrRobotConfig;
extern SingleSensorObjectInfo GstrRobotSensorInfo[ROBOT_SENSOR_NUMBER];
extern uint8_t Gu8RobotSysDistance[ROBOT_SENSOR_NUMBER];

// 函数声明

/**
 * @brief 机器人超声波系统初始化
 */
void Robot_UltrasonicInit(void);

/**
 * @brief 机器人超声波主任务
 */
void Robot_UltrasonicTask(void);

/**
 * @brief 处理指定方向的传感器数据
 * @param direction 方向索引
 * @param burst_pattern 发波模式
 */
void Robot_ProcessDirection(uint8_t direction, uint16_t burst_pattern);

/**
 * @brief 方向级数据融合
 * @param direction 方向索引
 */
void Robot_DirectionDataFusion(uint8_t direction);

/**
 * @brief 选择发波策略
 * @param motion_state 运动状态
 * @param direction_index 方向索引
 * @return 发波模式
 */
uint16_t Robot_SelectBurstPattern(tenu_RobotMotionStateTypedef motion_state, uint8_t direction_index);

/**
 * @brief 获取自适应延时
 * @param motion_state 运动状态
 * @return 延时时间(ms)
 */
uint16_t Robot_GetAdaptiveDelay(tenu_RobotMotionStateTypedef motion_state);

/**
 * @brief 更新警告系统
 */
void Robot_UpdateWarningSystem(void);

/**
 * @brief 全局数据处理
 */
void Robot_GlobalProcessing(void);

/**
 * @brief 获取机器人运动状态
 * @return 运动状态
 */
tenu_RobotMotionStateTypedef Robot_GetMotionState(void);

/**
 * @brief 设置机器人运动状态
 * @param motion_state 运动状态
 */
void Robot_SetMotionState(tenu_RobotMotionStateTypedef motion_state);

/**
 * @brief 获取指定方向的距离
 * @param direction 方向
 * @return 距离(mm)，NO_OBJECT表示无障碍物
 */
uint16_t Robot_GetDirectionDistance(tenu_RobotDirectionTypedef direction);

/**
 * @brief 获取指定方向的警告等级
 * @param direction 方向
 * @return 警告等级
 */
tenu_RobotWarningLevelTypedef Robot_GetDirectionWarningLevel(tenu_RobotDirectionTypedef direction);

/**
 * @brief 检查是否可以安全移动
 * @param direction 移动方向
 * @return 1-可以移动，0-不可移动
 */
uint8_t Robot_IsSafeToMove(tenu_RobotDirectionTypedef direction);

/**
 * @brief 计算数据可信度
 * @param valid1 传感器1数据有效性
 * @param valid2 传感器2数据有效性
 * @param dist1 传感器1距离
 * @param dist2 传感器2距离
 * @return 可信度(0-100)
 */
uint8_t Robot_CalculateConfidence(uint8_t valid1, uint8_t valid2, uint16_t dist1, uint16_t dist2);

/**
 * @brief 调试：简单距离测试，绕过复杂滤波
 */
void Robot_SimpleDistanceTest(void);

/**
 * @brief 调试：打印原始传感器数据
 */
void Robot_DebugPrintSensorData(void);

#ifdef __cplusplus
}
#endif

#endif /* __ROBOT_ULTRASONIC_H__ */
