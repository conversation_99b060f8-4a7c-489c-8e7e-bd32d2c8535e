#include <stdint.h>
#include <math.h>
#include "ultrasonic_alg.h"
#include "Modbus.h"
#include "modbus_interface.h"
#include "echo_processing.h"

// 三角测量核心算法(移植自E52436)
void Triangulation_CalculatePosition(SensorEchoInfoTypedef* master,
                                   SensorEchoInfoTypedef* slave,
                                   float* x, float* y)
{
    // 1. 获取主发和侦听距离
    uint16_t master_dis = master->EchoInfo[0].u16EchoTime;
    uint16_t slave_dis = slave->EchoInfo[0].u16EchoTime;
    
    // 2. 距离有效性检查
    if(master_dis == 0xFFFF || slave_dis == 0xFFFF) {
        *x = *y = 0xFFFF;
        return;
    }

    // 3. 计算余弦值(余弦定理)
    float cos_theta = 0;
    uint16_t sensor_spacing =  CENTER_CENTER;
    
    // 余弦定理: cosθ = (b² + c² - a²)/2bc
    float a_sq = slave_dis * slave_dis;
    float b_sq = master_dis * master_dis;
    float c_sq = sensor_spacing * sensor_spacing;
    
    cos_theta = (b_sq + c_sq - a_sq) / (2 * master_dis * sensor_spacing);
    
    // 限制cos值范围[-1,1]
    if(cos_theta > 1.0f) cos_theta = 1.0f;
    if(cos_theta < -1.0f) cos_theta = -1.0f;
    
    // 4. 计算坐标(考虑探头安装角度)
    float sin_theta = sqrtf(1 - cos_theta*cos_theta);
    
    if(0) {
        // 边角探头计算
        *x = (master_dis * (cos_theta * PLAT_COR_COSB/100.0f - 
                          sin_theta * PLAT_COR_SINB/100.0f));
        *y = (master_dis * (sin_theta * PLAT_COR_COSB/100.0f + 
                          cos_theta * PLAT_COR_SINB/100.0f));
    } else {
        // 中间探头计算
        *x = master_dis * cos_theta;
        *y = master_dis * sin_theta;
    }
    
    // 5. 单位转换(mm->m)
    *x /= 1000.0f;
    *y /= 1000.0f;
}

// 温度补偿函数(移植自E52436)
uint16_t TEMP_CORRECTION(uint8_t temp) 
{
    // 温度补偿公式: 3314 + (T-50)*6 (单位: 0.1m/s)
    uint32_t sound_speed = 3314 + (temp - 50) * 6;
    return (uint16_t)(sound_speed / 10); // 返回cm/ms
}
