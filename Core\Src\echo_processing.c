#include "main.h"
#include "echo_processing.h"
#include "algorithm.h"
#include "triangulation.h"
#include "ultrasonic_alg.h"
#include "Modbus.h"
#include "modbus_interface.h"
#include "debug_config.h"
#include "math.h"
#include <stdlib.h> // For abs

// Modbus处理句柄
extern modbusHandler_t ModbusH2;

// 从E52436移植的常量定义
#define CONFIDENCE_MIN_VAL 50
#define DNS_SLOW_TAU 6
#define DIFF_DISTANCE 250
#define ABS(a, b) ((a) > (b) ? ((a) - (b)) : ((b) - (a)))
#define ABS_VALUE(a) ((a) > 0 ? (a) : -(a))

// 全局变量定义
SensorEchoInfoTypedef GstrSnsEchoInfo;
SystemObjInfoTypedef GstrSystemObjInfo;
SingleSensorObjectInfo GstrSensorObjInfo[SENSOR_NUMBER], GstrSensorObjInfoBkp[SENSOR_NUMBER];
uint8_t Gu8SensorRingErrCnt[SENSOR_NUMBER], Gu8SnsRingAddCnt[SENSOR_NUMBER], Gu8SnsRingAddFlag[SENSOR_NUMBER];
uint8_t Lu8RingStableCnt[SENSOR_NUMBER];
uint8_t Gu8SysDistance[SENSOR_NUMBER] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
uint8_t Gu8SnsDisUpdateCnt[SENSOR_NUMBER][3];
uint8_t Gu8SysWarningZone[SENSOR_NUMBER];
uint8_t Gu8SensorErrSta[SENSOR_NUMBER] = {0};
static uint8_t Su8ListenFlg[SENSOR_NUMBER] = {0};
static uint8_t Su8ListenFlgBkp[SENSOR_NUMBER] = {0};
uint8_t Gu8_MarstDis[SENSOR_NUMBER][3];
uint8_t Gu8CompareTime = 1; // Placeholder for vehicle speed related logic
uint8_t Gu8SpeedData = 0; // Placeholder for vehicle speed data

Statistic stdevpx;
Statistic_16 stEchoVoltage;

// 新增的错误状态管理变量
uint8_t Gu8SensorComErrFlag = 0;              // 传感器通信错误标志
uint8_t Gu8InblindErrFlag = 0;                // 盲区错误标志
uint8_t Gu8ModbusTimeoutCnt[SENSOR_NUMBER] = {0}; // Modbus超时计数
uint8_t Gu8ModbusRetryCnt[SENSOR_NUMBER] = {0};   // Modbus重试计数

// 错误检测计数器
static uint8_t Gu8FreqErrCnt[SENSOR_NUMBER] = {0};
static uint8_t Gu8TempErrCnt[SENSOR_NUMBER] = {0};
static uint8_t Gu8BlockErrCnt[SENSOR_NUMBER] = {0};
static uint8_t GU8FrequencyNormolAfterPowerOn = 0;

// 用于碎石：在此回波时间范围内出现稳定主发和侦听的情况，通过回波电压参数来过滤
uint16_t gu16EchoTimex[2] = {5000, 7800};

// 函数声明
static void UpdateRingingTime(tenu_SensorIDTypedef LenuSensorID);
static uint8_t ValueUpdateDistance(uint8_t *Lu8Distance, tenu_SensorIDTypedef LenuSensorID);
static void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID);
static float CalObjCosAngle(uint16_t LwSnsDis, uint16_t LwMasterDis, uint16_t LwOriLisDis, tenu_SensorIDTypedef LenuSensorID);
static void CalcObjCoord(tenu_SensorIDTypedef LenuSensorID);
static uint16_t CalcTriangleDistance(uint16_t LwListenDistanceTime);
static uint8_t updateStatistic(Statistic *stat, uint16_t newData);
static uint8_t updateStatistic2(Statistic_16 *stat, uint16_t newData);
static uint8_t GetEchoVoltageInfo(void);
static void initStatistic(Statistic *stat);

#define TEMP_CORRECTION(temp) (3314 + ((temp) - 50) * 6)

// Modbus ID到内部传感器ID的映射 (扩展到8传感器)
static tenu_SensorIDTypedef ModbusIdToSensorId(uint8_t modbus_id)
{
    switch(modbus_id) {
        case 1: return SENSOR_F1;  // 前左 (原ROL)
        case 2: return SENSOR_F2;  // 前右 (原RCL)
        case 3: return SENSOR_R1;  // 右前 (原RCR)
        case 4: return SENSOR_R2;  // 右后 (原ROR)
        case 5: return SENSOR_B1;  // 后右 (新增)
        case 6: return SENSOR_B2;  // 后左 (新增)
        case 7: return SENSOR_L1;  // 左后 (新增)
        case 8: return SENSOR_L2;  // 左前 (新增)
        default: return SENSOR_NONE;
    }
}

// 获取相邻传感器ID (环形排列，支持8传感器)
static uint8_t GetAdjacentSensorId(uint8_t center_id, int8_t offset, uint8_t max_sensors)
{
    int8_t result = center_id + offset;
    if(result < 1) result += max_sensors;
    if(result > max_sensors) result -= max_sensors;
    return (uint8_t)result;
}

void initStatistic(Statistic *stat)
{
    stat->mean = 0;
    stat->variance = 0;
    stat->head = 0;
    stat->full = 0;
    for (int i = 0; i < STDEVP_LEN; i++)
    {
        stat->buffer[i] = 0;
    }
}

uint8_t updateStatistic(Statistic *stat, uint16_t newData)
{
    uint32_t sta_sum = 0;
    uint8_t i = 0, delta = 0;

    stat->buffer[stat->head] = newData;
    stat->head = (stat->head + 1) % STDEVP_LEN;

    if (!stat->full && stat->head == 0)
    {
        stat->full = 1;
    }

    for (i = 0; i < STDEVP_LEN; i++)
    {
        sta_sum += stat->buffer[i];
    }
    stat->mean = sta_sum >> 5;

    sta_sum = 0;
    for (i = 0; i < STDEVP_LEN; i++)
    {
        delta = ABS(stat->buffer[i], stat->mean);
        sta_sum += delta * delta;
    }
    stat->variance = sta_sum >> 5;

    return stat->full;
}

uint8_t updateStatistic2(Statistic_16 *stat, uint16_t newData)
{
    uint32_t sta_sum = 0;
    uint8_t i = 0;

    stat->buffer[stat->head] = newData;
    stat->head = (stat->head + 1) % STDEVP_16_LEN;

    if (!stat->full && stat->head == 0)
    {
        stat->full = 1;
    }

    for (i = 0; i < STDEVP_16_LEN; i++)
    {
        sta_sum += stat->buffer[i];
    }
    stat->mean = sta_sum >> 3;

    return stat->full;
}

uint8_t GetCurrentNoiseInfo(void)
{
    uint8_t i, sum = 0;

    for (i = 0; i < SENSOR_NUMBER; i++)
    {
        sum += GstrSensorObjInfo[i].u8NoiseCnt[1]; // Assuming second noise count is relevant
    }

    if (updateStatistic(&stdevpx, sum) == 1)
    {
        return stdevpx.variance;
    }
    return 0;
}

uint8_t GetEchoVoltageInfo(void)
{
    uint16_t sum = 0;

    for (uint8_t i = SENSOR_ROL; i <= SENSOR_ROR; i++)
    {
        if (Gu8SysDistance[i] != NO_OBJECT && Gu8SysDistance[i] != 0xFF)
        {
            sum += GstrSensorObjInfo[i].u8EchoVoltageLevel[SENSOR_MASTER_BURST];
            if (Su8ListenFlg[i])
            {
                sum += GstrSensorObjInfo[i].u8EchoVoltageLevel[Su8ListenFlg[i]];
            }
        }      
    }
    updateStatistic2(&stEchoVoltage, sum);

    return 0;
}

void UltrasonicAlg_Init(void)
{
    initStatistic(&stdevpx);

    stEchoVoltage.head = 0;
    stEchoVoltage.full = 0;
    for(int i=0; i<STDEVP_16_LEN; i++) {
        stEchoVoltage.buffer[i] = 0;
    }

    for(int i=0; i<SENSOR_NUMBER; i++) {
        Gu8SysDistance[i] = NO_OBJECT;
        Gu8SensorRingErrCnt[i] = 0;
        Gu8SnsRingAddCnt[i] = 0;
        Gu8SnsRingAddFlag[i] = 0;
        Su8ListenFlg[i] = 0;
        Su8ListenFlgBkp[i] = 0;
        Gu8SensorErrSta[i] = ERR_STA_NULL;
        Gu8ModbusTimeoutCnt[i] = 0;
        Gu8ModbusRetryCnt[i] = 0;
        Gu8FreqErrCnt[i] = 0;
        Gu8TempErrCnt[i] = 0;
        Gu8BlockErrCnt[i] = 0;
        for(int j=0; j<3; j++) {
            Gu8SnsDisUpdateCnt[i][j] = 0;
        }
    }

    Gu8SensorComErrFlag = 0;
    Gu8InblindErrFlag = 0;
    GU8FrequencyNormolAfterPowerOn = 0;
    Gu8CompareTime = NOISE_COMPARE_TIME;

    // 初始化备份数组，避免第一次运行时Delta过大
    memset(GstrSensorObjInfo, 0, sizeof(GstrSensorObjInfo));
    memset(GstrSensorObjInfoBkp, 0, sizeof(GstrSensorObjInfoBkp));
    memset(&GstrSystemObjInfo, 0, sizeof(GstrSystemObjInfo));

    // 初始化系统警告信息
    InitSystemWarningInfo();
}

/******************************************************************************
 * 函数名称: SensorErrorCheck
 * 功能描述: 传感器错误状态综合检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 综合检查所有传感器错误状态
 *******************************************************************************/
int SensorErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    // 检查通信错误
    if (US_GET_BIT(Gu8SensorComErrFlag, sensor_id)) {
        Gu8SensorErrSta[sensor_id] = ERR_STA_COM;
    }
    // 检查余震错误
    else if (Gu8SensorRingErrCnt[sensor_id] > (RING_ERR_CONFIRM - 1)) {
        Gu8SensorErrSta[sensor_id] = ERR_STA_RING;
    }
    // 检查频率异常
    else if ((GstrSensorObjInfo[sensor_id].u8SnsStaFlg & SNS_STA_FREQ_ERR_FLAG) ||
             (GstrSensorObjInfo[sensor_id].u8SnsStaFlg & SNS_STA_BLOCK_FLAG)) {
        if (GU8FrequencyNormolAfterPowerOn < 0x0F) {
            Gu8SensorErrSta[sensor_id] = ERR_STA_FRE;
        } else {
            Gu8SensorErrSta[sensor_id] = ERR_STA_BLOCK;
        }
    }
    // 检查过温
    else if (GstrSensorObjInfo[sensor_id].u8SnsStaFlg & SNS_STA_OVER_TEMP_FLAG) {
        Gu8SensorErrSta[sensor_id] = ERR_STA_OVER_TEMPERATION;
    }
    // 检查噪声异常
    else if (GstrSystemObjInfo.cNosicCnt > (NOISE_CNT_MAX + 3)) {
        Gu8SensorErrSta[sensor_id] = ERR_STA_NOISE;
    }
    // 检查盲区错误
    else if (US_GET_BIT(Gu8InblindErrFlag, sensor_id)) {
        Gu8SensorErrSta[sensor_id] = ERR_STA_IN_BLIND;
    }
    else {
        Gu8SensorErrSta[sensor_id] = ERR_STA_NULL;
    }

    // 智能传感器错误状态处理 - 仅记录状态，不清空距离数据
    if (Gu8SensorErrSta[sensor_id] != ERR_STA_NULL) {
        DEBUG_SENSOR_ERROR("SensorErrorCheck[%d] - SensorErrSta=%d (recorded but not clearing distance)\r\n",
                           sensor_id, Gu8SensorErrSta[sensor_id]);

        // 注意：这个函数在距离计算完成后调用，所以不应该清空距离数据
        // 错误状态仅用于记录和警告系统，距离数据的有效性已经在计算过程中验证过了
    }

    return 0;
}

/******************************************************************************
 * 函数名称: SnsBlockageErrorCheck
 * 功能描述: 传感器阻塞错误检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检查传感器是否被阻塞
 *******************************************************************************/
int SnsBlockageErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    if (GstrSensorObjInfo[sensor_id].u8SampleFreq <= (uint8_t)(BUTSR_FREQUENCY_KHZ * 0.8)) {
        // 频率过小
        Gu8BlockErrCnt[sensor_id]++;
        if (Gu8BlockErrCnt[sensor_id] >= FREQ_ERR_CONFIRM_CNT) {
            Gu8BlockErrCnt[sensor_id] = FREQ_ERR_CONFIRM_CNT;
            GstrSensorObjInfo[sensor_id].u8SnsStaFlg |= SNS_STA_BLOCK_FLAG;
        }
    } else {
        Gu8BlockErrCnt[sensor_id] = 0;
        GstrSensorObjInfo[sensor_id].u8SnsStaFlg &= ~SNS_STA_BLOCK_FLAG;
    }

    return 0;
}

/******************************************************************************
 * 函数名称: SnsOverTemperationErrorCheck
 * 功能描述: 传感器过温错误检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检查传感器是否过温
 *******************************************************************************/
int SnsOverTemperationErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    // 假设温度数据在GstrSnsEchoInfo.uTemperature中，需要转换为实际温度
    uint16_t actual_temp = GstrSnsEchoInfo.uTemperature - 50; // 根据实际温度转换公式调整

    if (actual_temp > MAX_TEMP_THRESHOLD) {
        Gu8TempErrCnt[sensor_id]++;
        if (Gu8TempErrCnt[sensor_id] >= TEMP_ERR_CONFIRM_CNT) {
            Gu8TempErrCnt[sensor_id] = TEMP_ERR_CONFIRM_CNT;
            GstrSensorObjInfo[sensor_id].u8SnsStaFlg |= SNS_STA_OVER_TEMP_FLAG;
        }
    } else {
        Gu8TempErrCnt[sensor_id] = 0;
        GstrSensorObjInfo[sensor_id].u8SnsStaFlg &= ~SNS_STA_OVER_TEMP_FLAG;
    }

    return 0;
}

/******************************************************************************
 * 函数名称: SnsFrequencyErrorCheck
 * 功能描述: 传感器频率异常检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检查传感器频率是否异常
 *******************************************************************************/
int SnsFrequencyErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    // 检查频率是否过大
    if (GstrSensorObjInfo[sensor_id].u8SampleFreq > (uint8_t)(BUTSR_FREQUENCY_KHZ + (BUTSR_FREQUENCY_KHZ / 2))) {
        Gu8FreqErrCnt[sensor_id]++;
        if (Gu8FreqErrCnt[sensor_id] >= FREQ_ERR_CONFIRM_CNT) {
            Gu8FreqErrCnt[sensor_id] = FREQ_ERR_CONFIRM_CNT;
            GstrSensorObjInfo[sensor_id].u8SnsStaFlg |= SNS_STA_FREQ_ERR_FLAG;
        }
    } else {
        Gu8FreqErrCnt[sensor_id] = 0;
        GstrSensorObjInfo[sensor_id].u8SnsStaFlg &= ~SNS_STA_FREQ_ERR_FLAG;
    }

    // 更新频率正常标志
    if ((!(GstrSensorObjInfo[sensor_id].u8SnsStaFlg & SNS_STA_BLOCK_FLAG)) &&
        (!(GstrSensorObjInfo[sensor_id].u8SnsStaFlg & SNS_STA_FREQ_ERR_FLAG))) {
        if (GU8FrequencyNormolAfterPowerOn < 0x0F) {
            GU8FrequencyNormolAfterPowerOn++;
        }
    }

    return 0;
}

/******************************************************************************
 * 函数名称: SnsRingErrorCheck
 * 功能描述: 传感器余震错误检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检查传感器余震是否异常
 *******************************************************************************/
int SnsRingErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    // 检查余震时间是否在正常范围内
    if ((GstrSensorObjInfo[sensor_id].u16RingingTime < RING_MIN) ||
        (GstrSensorObjInfo[sensor_id].u16RingingTime > RING_MAX)) {
        Gu8SensorRingErrCnt[sensor_id]++;
        if (Gu8SensorRingErrCnt[sensor_id] > RING_ERR_CONFIRM) {
            Gu8SensorRingErrCnt[sensor_id] = RING_ERR_CONFIRM;
        }
    } else {
        if (Gu8SensorRingErrCnt[sensor_id] > 0) {
            Gu8SensorRingErrCnt[sensor_id]--;
        }
    }

    return 0;
}

/******************************************************************************
 * 函数名称: SnsModbusComErrorCheck
 * 功能描述: 传感器Modbus通信错误检查
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 0 - 成功
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检查Modbus通信是否正常
 *******************************************************************************/
int SnsModbusComErrorCheck(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return -1;

    // 检查Modbus超时计数
    if (Gu8ModbusTimeoutCnt[sensor_id] > MODBUS_TIMEOUT_CNT) {
        US_SET_BIT(Gu8SensorComErrFlag, sensor_id);

        // 如果重试次数未达到最大值，尝试重试
        if (Gu8ModbusRetryCnt[sensor_id] < MODBUS_RETRY_MAX) {
            Gu8ModbusRetryCnt[sensor_id]++;
            Gu8ModbusTimeoutCnt[sensor_id] = 0; // 重置超时计数
        }
    } else {
        US_CLR_BIT(Gu8SensorComErrFlag, sensor_id);
        Gu8ModbusRetryCnt[sensor_id] = 0; // 重置重试计数
    }

    return 0;
}

/******************************************************************************
 * 函数名称: UpdateDynamicCompareTime
 * 功能描述: 更新动态比较时间
 * 输入参数: 无
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 根据噪声情况动态调整比较时间
 *******************************************************************************/
void UpdateDynamicCompareTime(void)
{
    uint8_t noise_cnt = GetCurrentNoiseInfo();

    if (noise_cnt >= NOISE_CNT_MAX) {
        if (noise_cnt > 23) {
            Gu8CompareTime = NOISE_COMPARE_TIME + 2;
        } else {
            if (ABS(GstrSystemObjInfo.cNosicCnt, noise_cnt) < 3) {
                Gu8CompareTime = NOISE_COMPARE_TIME;
            } else {
                Gu8CompareTime = NOISE_COMPARE_TIME + 1;
            }
        }
    } else {
        Gu8CompareTime = NOISE_COMPARE_TIME;
    }
}

/******************************************************************************
 * 函数名称: Echo_Private_InBlind
 * 功能描述: 盲区检测
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 检测传感器是否在盲区内
 *******************************************************************************/
static void Echo_Private_InBlind(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return;

    if ((Gu8_MarstDis[sensor_id][SENSOR_MASTER_BURST] < FINAL_DISTANCE_LIMIT) ||
        (Gu8SysDistance[sensor_id] <= FINAL_DISTANCE_LIMIT)) {
        US_SET_BIT(Gu8InblindErrFlag, sensor_id);
    } else {
        US_CLR_BIT(Gu8InblindErrFlag, sensor_id);
    }
}

/******************************************************************************
 * 函数名称: CalcWarningZone
 * 功能描述: 计算警告分段
 * 输入参数: LenuSensorID - 传感器ID, Lu8Distance - 距离
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 根据距离计算警告分段
 *******************************************************************************/
void CalcWarningZone(tenu_SensorIDTypedef LenuSensorID, uint8_t Lu8Distance)
{
    if(LenuSensorID >= SENSOR_NUMBER) return;

    uint8_t max_distance;

    // 根据传感器类型设置最大距离
    if (LenuSensorID == SENSOR_ROL || LenuSensorID == SENSOR_ROR) {
        max_distance = CORNER_FAR_WARN_DIS;
    } else {
        max_distance = CENTER_RAR_WARN_DIS;
    }

    // 距离超出最大范围
    if (Lu8Distance > max_distance) {
        Lu8Distance = NO_OBJECT;
    }

    // 计算警告分段
    if (Lu8Distance >= max_distance) {
        Gu8SysWarningZone[LenuSensorID] = 0;  // 无警告
    } else if (Lu8Distance > MIDDLE_WARN_DIS) {
        Gu8SysWarningZone[LenuSensorID] = 3;  // 远距离警告
    } else if (Lu8Distance > NEAR_WARN_DIS) {
        Gu8SysWarningZone[LenuSensorID] = 2;  // 中距离警告
    } else {
        Gu8SysWarningZone[LenuSensorID] = 1;  // 近距离警告
    }
}

/******************************************************************************
 * 函数名称: InitSystemWarningInfo
 * 功能描述: 初始化系统警告信息
 * 输入参数: 无
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 初始化系统警告相关的配置和状态
 *******************************************************************************/
void InitSystemWarningInfo(void)
{
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        Gu8SysWarningZone[i] = 0;
    }
}

/******************************************************************************
 * 函数名称: GetSensorMaxDistance
 * 功能描述: 获取传感器最大检测距离
 * 输入参数: sensor_id - 传感器ID
 * 返回参数: 最大检测距离
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 根据传感器类型返回不同的最大检测距离
 *******************************************************************************/
uint8_t GetSensorMaxDistance(tenu_SensorIDTypedef sensor_id)
{
    if(sensor_id >= SENSOR_NUMBER) return 0;

    // 根据传感器位置设置不同的最大距离
    switch(sensor_id) {
        case SENSOR_ROL:  // 左后角
        case SENSOR_ROR:  // 右后角
            return CORNER_FAR_WARN_DIS;  // 角传感器距离较短

        case SENSOR_RCL:  // 左后中
        case SENSOR_RCR:  // 右后中
            return CENTER_RAR_WARN_DIS;  // 中央传感器距离较长

        default:
            return CENTER_RAR_WARN_DIS;
    }
}

/******************************************************************************
 * 函数名称: GetWarningLevel
 * 功能描述: 获取警告等级
 * 输入参数: sensor_id - 传感器ID, distance - 距离
 * 返回参数: 警告等级 (0-无警告, 1-近距离, 2-中距离, 3-远距离)
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 根据距离和传感器类型计算警告等级
 *******************************************************************************/
uint8_t GetWarningLevel(tenu_SensorIDTypedef sensor_id, uint8_t distance)
{
    if(sensor_id >= SENSOR_NUMBER) return 0;

    uint8_t max_distance = GetSensorMaxDistance(sensor_id);

    // 距离超出最大范围或无效
    if (distance >= max_distance || distance == NO_OBJECT) {
        return 0;  // 无警告
    }

    // 根据距离分段计算警告等级
    if (distance <= NEAR_WARN_DIS) {
        return 1;  // 近距离警告 (最高级别)
    } else if (distance <= MIDDLE_WARN_DIS) {
        return 2;  // 中距离警告
    } else {
        return 3;  // 远距离警告 (最低级别)
    }
}

/******************************************************************************
 * 函数名称: UpdateSystemWarningInfo
 * 功能描述: 更新系统警告信息
 * 输入参数: 无
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 更新所有传感器的警告状态
 *******************************************************************************/
void UpdateSystemWarningInfo(void)
{
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        // 只有在传感器无严重错误时才计算警告
        if ((Gu8SensorErrSta[i] == ERR_STA_NULL) ||
            (Gu8SensorErrSta[i] == ERR_STA_SW) ||
            (Gu8SensorErrSta[i] == ERR_STA_HW) ||
            (Gu8SensorErrSta[i] == ERR_STA_NOISE) ||
            (Gu8SensorErrSta[i] == ERR_STA_IN_BLIND)) {

            Gu8SysWarningZone[i] = GetWarningLevel((tenu_SensorIDTypedef)i, Gu8SysDistance[i]);
        } else {
            // 传感器有严重错误时，清除警告
            Gu8SysWarningZone[i] = 0;
        }
    }
}

/******************************************************************************
 * 函数名称: ProcessSystemWarning
 * 功能描述: 处理系统警告
 * 输入参数: 无
 * 返回参数: 无
 * 设计日期: 2024-07-04
 * 修改内容: 初版
 * 其它说明: 处理系统级别的警告逻辑，包括多传感器协同判断
 *******************************************************************************/
void ProcessSystemWarning(void)
{
    uint8_t max_warning_level = 0;
    uint8_t warning_sensor_count = 0;

    // 统计最高警告等级和警告传感器数量
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        if(Gu8SysWarningZone[i] > 0) {
            warning_sensor_count++;
            if(Gu8SysWarningZone[i] > max_warning_level) {
                max_warning_level = Gu8SysWarningZone[i];
            }
        }
    }

    // 根据警告情况进行系统级处理
    if(max_warning_level > 0) {
        // 可以在这里添加蜂鸣器、LED指示等警告输出
        // 例如：根据警告等级调整蜂鸣器频率
        switch(max_warning_level) {
            case 1:  // 近距离警告 - 快速蜂鸣
                // SetBuzzerFrequency(FAST_BEEP);
                break;
            case 2:  // 中距离警告 - 中速蜂鸣
                // SetBuzzerFrequency(MEDIUM_BEEP);
                break;
            case 3:  // 远距离警告 - 慢速蜂鸣
                // SetBuzzerFrequency(SLOW_BEEP);
                break;
        }

        // 多传感器同时警告时的特殊处理
        if(warning_sensor_count >= 2) {
            // 可能是大型障碍物，增强警告
            // EnhanceWarning();
        }
    } else {
        // 无警告时关闭蜂鸣器
        // SetBuzzerFrequency(NO_BEEP);
    }
}



// 完整的多探头协同处理流程
void Ultrasonic_ProcessData(SensorEchoInfoTypedef *echo, uint16_t burst_ctrl_id)
{
    // 1. 发波控制 - 广播发波命令
    if(US_SetBurstMode(&ModbusH2, burst_ctrl_id) != MODBUS_Q_OK) {
        return; // 发波失败
    }

    // 2. 等待测距完成
    osDelay(50); // 增加延时确保测距完成

    // 3. 读取系统温度 (用于声速补偿)
    float temp;
    if(US_ReadTemperature(&ModbusH2, 0x01, &temp) != MODBUS_Q_OK) {
        temp = 25.0f; // 默认温度
    }

    // 4. 处理每个主发探头的数据 (扩展到8传感器)
    for(uint8_t modbus_id = 1; modbus_id <= 8; modbus_id++) {
        if(!(burst_ctrl_id & (0x01 << (modbus_id - 1)))) {
            continue; // 跳过非主发探头
        }

        tenu_SensorIDTypedef sensor_id = ModbusIdToSensorId(modbus_id);
        if(sensor_id == SENSOR_NONE) continue;

        // 5. 收集主发探头和相邻侦听探头的数据
        SensorEchoInfoTypedef sensor_data;
        sensor_data.uTemperature = (uint16_t)(temp * 10); // 温度*10存储

        // 5.1 读取主发探头自身数据
        if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
            // 5.2 读取左侧侦听探头数据 (如果不是主发) - 支持8传感器
            uint8_t left_id = GetAdjacentSensorId(modbus_id, -1, 8);
            if(!(burst_ctrl_id & (0x01 << (left_id - 1)))) {
                SensorEchoInfoTypedef left_data;
                if(US_ReadEchoInfo(&ModbusH2, left_id, &left_data) == MODBUS_Q_OK) {
                    // 将左侧数据作为侦听数据存储
                    Su8ListenFlg[sensor_id] = SENSOR_LISTEN_LEFT;
                }
            }

            // 5.3 读取右侧侦听探头数据 (如果不是主发) - 支持8传感器
            uint8_t right_id = GetAdjacentSensorId(modbus_id, 1, 8);
            if(!(burst_ctrl_id & (0x01 << (right_id - 1)))) {
                SensorEchoInfoTypedef right_data;
                if(US_ReadEchoInfo(&ModbusH2, right_id, &right_data) == MODBUS_Q_OK) {
                    // 将右侧数据作为侦听数据存储
                    if(Su8ListenFlg[sensor_id] == 0) {
                        Su8ListenFlg[sensor_id] = SENSOR_LISTEN_RIGHT;
                    }
                }
            }

            // 6. 调用滤波和处理算法 - 传递正确的传感器ID
            UltrasonicAlg_ProcessData(&sensor_data, sensor_id);
        }
    }
}


// 完整的发波-收波处理流程 - 修复版本，支持指定传感器ID
void UltrasonicAlg_ProcessData(SensorEchoInfoTypedef* data, tenu_SensorIDTypedef sensor_id)
{
    // 验证传感器ID有效性
    if(sensor_id >= SENSOR_NUMBER) {
        printf("ERROR: Invalid sensor_id=%d\r\n", sensor_id);
        return; // 无效的传感器ID
    }

    // 调试输出：显示正在更新哪个传感器的数据
    //printf("DEBUG: Updating GstrSensorObjInfo[%d] with echo data (EchoTime=%d, RingingTime=%d, SampleFreq=%d)\r\n",
    //       sensor_id, data->EchoInfo[0].u16EchoTime, data->u16RingingTime, data->u8SampleFreq);

    // 1. 备份当前数据用于比较
    memcpy(&GstrSensorObjInfoBkp[sensor_id], &GstrSensorObjInfo[sensor_id], sizeof(SingleSensorObjectInfo));

    // 2. 更新传感器数据结构
    // 映射回波数据到内部数据结构
    for(int i = 0; i < SENSOR_LISTEN_NUMBER && i < MAX_ECHO_CNT; i++) {
        GstrSensorObjInfo[sensor_id].u16EchoTime[i] = data->EchoInfo[i].u16EchoTime;
        GstrSensorObjInfo[sensor_id].u16EchoWidth[i] = data->EchoInfo[i].u16EchoWidth;
        GstrSensorObjInfo[sensor_id].u8EchoVoltageLevel[i] = data->EchoInfo[i].u8EchoVoltageLevel;
        GstrSensorObjInfo[sensor_id].u8EchoConfidence[i] = data->EchoInfo[i].u8EchoConfidence;
    }

    // 更新基本信息
    GstrSensorObjInfo[sensor_id].u16RingingTime = data->u16RingingTime;
    GstrSensorObjInfo[sensor_id].u8SampleFreq = data->u8SampleFreq;
    GstrSensorObjInfo[sensor_id].u8SnsStaFlg = data->u8SnsStaFlg;

    // 更新噪声计数 - 正确映射到各个通道
    for(int i = 0; i < SENSOR_LISTEN_NUMBER; i++) {
        GstrSensorObjInfo[sensor_id].u8NoiseCnt[i] = data->u8NoiseCnt;
    }

    // 更新全局温度信息
    GstrSnsEchoInfo.uTemperature = data->uTemperature;

    // 3. 调用原项目的主处理函数
    CalcSnsObjDistance(sensor_id);

    // 4. 更新回波电压统计信息
    GetEchoVoltageInfo();

    // 5. 更新噪声统计信息
    GetCurrentNoiseInfo();

    // 6. 更新动态比较时间
    UpdateDynamicCompareTime();

    // 7. 更新系统警告信息
    UpdateSystemWarningInfo();

    // 8. 处理系统警告
    ProcessSystemWarning();
}

static void UpdateRingingTime(tenu_SensorIDTypedef LenuSensorID)
{
    uint16_t Lu16RingDelta;
    Lu16RingDelta = ABS(GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime, GstrSensorObjInfo[LenuSensorID].u16RingingTime);
    if (GstrSensorObjInfo[LenuSensorID].u16RingingTime < RING_MIN)
    {
        Gu8SnsRingAddCnt[LenuSensorID] = 0u; /**< 清除余震变化计数 */
        Lu8RingStableCnt[LenuSensorID] = 0;
        /**< 余震故障计数累加 */
        if (Gu8SensorRingErrCnt[LenuSensorID] < RING_ERR_CONFIRM)
        {
            Gu8SensorRingErrCnt[LenuSensorID]++;
        }
    }
    else
    {
        /**< 逐步清除余震故障计数 */
        if (Gu8SensorRingErrCnt[LenuSensorID])
        {
            Gu8SensorRingErrCnt[LenuSensorID]--;
        }
        if (Lu16RingDelta > RING_CHANGE_THR)
        {
            if (!GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime || GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime == INVALID_ECHO_TIME)
            {
                Gu8SnsRingAddCnt[LenuSensorID] = 0u; /**< 清除余震变化计数 */
                Lu8RingStableCnt[LenuSensorID] = RING_STABLE_CNT;
            }
            else
            {
                /**< 回波有效 */
                if (GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] != INVALID_ECHO_TIME)
                {
                    Lu8RingStableCnt[LenuSensorID] = 0;
                    if (Lu16RingDelta < RING_ADD_LEVEL1)
                    {
                        if (Gu8SnsRingAddCnt[LenuSensorID] && GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] > DOUBLE_ECHO_TIME)
                        { // 余震变化小于RING_ADD_LEVEL1并且远距离有障碍物，减小余震近距离障碍物cnt
                            Gu8SnsRingAddCnt[LenuSensorID]--;
                        }
                    }
                    else if (Lu16RingDelta < RING_ADD_LEVEL2)
                    {
                        if (Gu8SnsRingAddCnt[LenuSensorID] < RING_CHANGE_CNT && GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] < DOUBLE_ECHO_TIME && (GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime < GstrSensorObjInfo[LenuSensorID].u16RingingTime))
                        { // 近距离有障碍物并且余震变大RING_ADD_LEVEL1 ~ RING_ADD_LEVEL2
                            Gu8SnsRingAddCnt[LenuSensorID]++;
                        }
                    }
                    else
                    {
                        if (GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime < GstrSensorObjInfo[LenuSensorID].u16RingingTime)
                        {
                            if (Gu8SnsRingAddCnt[LenuSensorID] < RING_CHANGE_CNT)
                            { // 余震变大RING_ADD_LEVEL2以上
                                Gu8SnsRingAddCnt[LenuSensorID]++;
                            }
                        }
                    }
                }
                else
                { // 无有效回波(障碍物)
                    if (Lu16RingDelta < RING_ADD_LEVEL1)
                    { // 余震变化小于RING_ADD_LEVEL1，减小余震近距离障碍物cnt、加大余震稳定cnt
                        if (Gu8SnsRingAddCnt[LenuSensorID])
                        {
                            Gu8SnsRingAddCnt[LenuSensorID]--;
                            Lu8RingStableCnt[LenuSensorID]++;
                        }
                    }
                    else if (Lu16RingDelta < RING_ADD_LEVEL2)
                    { // 余震变化RING_ADD_LEVEL1 ~ RING_ADD_LEVEL2，加大余震稳定cnt
                        Lu8RingStableCnt[LenuSensorID]++;
                    }
                    else
                    {
                        if (GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime < GstrSensorObjInfo[LenuSensorID].u16RingingTime)
                        { // 余震变大RING_ADD_LEVEL2以上，增大余震近距离障碍物cnt、减小余震稳定cnt
                            Gu8SnsRingAddCnt[LenuSensorID]++;
                            Lu8RingStableCnt[LenuSensorID]--;
                        }
                        else if (Gu8SnsRingAddCnt[LenuSensorID])
                        { // 余震变化RING_ADD_LEVEL2以上，减小余震近距离障碍物cnt、加大余震稳定cnt
                            Gu8SnsRingAddCnt[LenuSensorID]--;
                            Lu8RingStableCnt[LenuSensorID]++;
                        }
                    }
                }
            }
        }
        else
        { // 余震稳定
            Lu8RingStableCnt[LenuSensorID]++;
        }
    }
    if (Gu8SnsRingAddCnt[LenuSensorID] > RING_CHANGE_CNT / 2)
    { // 通过余震变大检测近距离障碍物有效
        Lu8RingStableCnt[LenuSensorID] = 0;
        Gu8SnsRingAddCnt[LenuSensorID] = RING_CHANGE_CNT;
        Gu8SnsRingAddFlag[LenuSensorID] = 1;
    }
    else if (Gu8SnsRingAddCnt[LenuSensorID] < RING_CHANGE_CNT / 3)
    {
        Gu8SnsRingAddCnt[LenuSensorID] = 0;
        Gu8SnsRingAddFlag[LenuSensorID] = 0;
    }
    if (Lu8RingStableCnt[LenuSensorID] >= RING_STABLE_CNT)
    {
        Gu8SnsRingAddCnt[LenuSensorID] = 0u; /**< 清除余震变化计数 */
        Lu8RingStableCnt[LenuSensorID] = 0;
        GstrSensorObjInfoBkp[LenuSensorID].u16RingingTime = GstrSensorObjInfo[LenuSensorID].u16RingingTime;
    }
}

uint16_t CalcTriangleDistance(uint16_t LwListenDistanceTime)
{
    if (LwListenDistanceTime == INVALID_ECHO_TIME || LwListenDistanceTime == 0x00)
    {
        return INVALID_ECHO_TIME;
    }
    else
    {
        // Simplified conversion, assuming temperature is handled elsewhere or using a standard value
        return LwListenDistanceTime / 58;
    }
}

float CalObjCosAngle(uint16_t LwSnsDis, uint16_t LwMasterDis, uint16_t LwOriLisDis, tenu_SensorIDTypedef LenuSensorID)
{
    float LfCosAngle = 0.0f;
    long LdwSnsDisSquare;
    long LdwMasterDisSquare;
    long LdwOppositeLisDisSquare;

    if ((abs(LwOriLisDis - LwMasterDis) < LwSnsDis) && ((LwOriLisDis + LwMasterDis) > LwSnsDis))
    {
        LdwSnsDisSquare = (long)LwSnsDis * LwSnsDis;
        LdwMasterDisSquare = (long)LwMasterDis * LwMasterDis;
        LdwOppositeLisDisSquare = (long)LwOriLisDis * LwOriLisDis;

        LfCosAngle = (LdwSnsDisSquare + LdwMasterDisSquare - LdwOppositeLisDisSquare);
        LfCosAngle = LfCosAngle / (LwMasterDis * LwSnsDis * 2.0f);

        if (LfCosAngle > 1.00f) LfCosAngle = 1.00f;
        else if (LfCosAngle < -1.00f) LfCosAngle = -1.00f;
    }
    else
    {
        LfCosAngle = 0.0f; // Default case if triangle inequality is not met
    }
    return LfCosAngle;
}

void CalcObjCoord(tenu_SensorIDTypedef LenuSensorID)
{
    float LfCosM = 0, LfCosR = 0;
    float Lu16SinM = 1.0f, Lu16SinR = 1.0f;

    if (GstrSystemObjInfo.u8ObjListenFlag[LenuSensorID])
    {
        uint16_t sensor_spacing = (LenuSensorID == SENSOR_RCL || LenuSensorID == SENSOR_RCR) ? CENTER_CENTER : PLAT_COR_CEN_DIS;
        LfCosM = CalObjCosAngle(sensor_spacing,
                                GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST],
                                GstrSystemObjInfo.u16OriginDis[LenuSensorID][GstrSystemObjInfo.u8ObjListenFlag[LenuSensorID]], LenuSensorID);
        
        if (fabs(LfCosM) <= 1.0f) {
            Lu16SinM = sqrtf(1.0f - LfCosM * LfCosM);
        } else {
            Lu16SinM = 0.0f;
        }

        LfCosR = LfCosM; // Simplified, no rotation for now
        Lu16SinR = Lu16SinM;
    }
    else
    {
        // No listening echo, assume object is straight ahead
        LfCosR = 1.0f;
        Lu16SinR = 0.0f;
    }

    DEBUG_COORDINATE("CalcObjCoord[%d] - EchoTime[MASTER]=%d, OriginDis[MASTER]=%d, ListenFlag=%d\r\n",
                     LenuSensorID, GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST],
                     GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST],
                     GstrSystemObjInfo.u8ObjListenFlag[LenuSensorID]);

    // 针对传感器1和3的详细调试
    uint8_t is_problematic_sensor = (LenuSensorID == 1 || LenuSensorID == 3);
    if (is_problematic_sensor) {
        printf("CalcObjCoord[%d]: ListenFlag=%d, LfCosM=%f, Lu16SinM=%f, LfCosR=%f, Lu16SinR=%f\r\n",
               LenuSensorID, GstrSystemObjInfo.u8ObjListenFlag[LenuSensorID],
               LfCosM, Lu16SinM, LfCosR, Lu16SinR);
    }

    if ((GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] != INVALID_ECHO_TIME) &&
        (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] != INVALID_ECHO_TIME))
    {
        GstrSystemObjInfo.s16ObjYcoord[LenuSensorID] = (uint16_t)(Lu16SinR * GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);
        GstrSystemObjInfo.s16ObjXcoord[LenuSensorID] = (int16_t)(LfCosR * GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);
        DEBUG_COORDINATE("CalcObjCoord[%d] - VALID: Y=%d, X=%d\r\n",
                         LenuSensorID, GstrSystemObjInfo.s16ObjYcoord[LenuSensorID], GstrSystemObjInfo.s16ObjXcoord[LenuSensorID]);
    }
    else
    {
        GstrSystemObjInfo.s16ObjYcoord[LenuSensorID] = 0xff;
        GstrSystemObjInfo.s16ObjXcoord[LenuSensorID] = 0xff;
        DEBUG_COORDINATE("CalcObjCoord[%d] - INVALID: Set to 0xFF\r\n", LenuSensorID);
    }
}

uint8_t ValueUpdateDistance(uint8_t *Lu8Distance, tenu_SensorIDTypedef LenuSensorID)
{
    uint8_t ret = 1;

    DEBUG_VALUE("ValueUpdateDistance[%d] - input=%d, SensorErrSta=%d\r\n",
                LenuSensorID, *Lu8Distance, Gu8SensorErrSta[LenuSensorID]);

    // 基本有效性检查
    if (*Lu8Distance == 0xff)
    {
        DEBUG_VALUE("ValueUpdateDistance[%d] - INVALID: distance is 0xFF (NO_OBJECT)\r\n", LenuSensorID);
        ret = 0;
    }
    else if (*Lu8Distance == 0x00)
    {
        // 修复：当Y坐标为0但有有效主发距离时，使用主发距离
        if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] != INVALID_ECHO_TIME &&
            GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] > 0) {
            *Lu8Distance = (uint8_t)GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST];
            DEBUG_VALUE("ValueUpdateDistance[%d] - Y=0 FIX: using MASTER distance %d\r\n", LenuSensorID, *Lu8Distance);
            ret = 1;
        } else {
            DEBUG_VALUE("ValueUpdateDistance[%d] - INVALID: distance is 0x00 (coordinate Y=0)\r\n", LenuSensorID);
            ret = 0;
        }
    }
    // 智能传感器错误状态检查
#if ENABLE_SENSOR_ERROR_TOLERANCE
    else if ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_NULL)
            && ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_SW)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_HW)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_IN_BLIND)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_NOISE)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_RING)))
    {
        DEBUG_CRITICAL("ValueUpdateDistance[%d] - ERROR_CHECK: state=%d, distance=%d\r\n",
                       LenuSensorID, Gu8SensorErrSta[LenuSensorID], *Lu8Distance);

        // 传感器有错误，但如果距离在合理范围内，给予容错
        if (*Lu8Distance >= FILTER_BYPASS_MIN_DISTANCE && *Lu8Distance <= FILTER_BYPASS_MAX_DISTANCE) {
            DEBUG_SENSOR_ERROR("ValueUpdateDistance[%d] - TOLERANCE: sensor error state=%d, but distance=%d is reasonable\r\n",
                               LenuSensorID, Gu8SensorErrSta[LenuSensorID], *Lu8Distance);
            DEBUG_CRITICAL("ValueUpdateDistance[%d] - TOLERANCE: allowing distance %d\r\n", LenuSensorID, *Lu8Distance);
            ret = 1; // 容错，允许通过
        } else {
            DEBUG_SENSOR_ERROR("ValueUpdateDistance[%d] - INVALID: sensor error state=%d, distance=%d unreasonable\r\n",
                               LenuSensorID, Gu8SensorErrSta[LenuSensorID], *Lu8Distance);
            DEBUG_CRITICAL("ValueUpdateDistance[%d] - REJECTING: distance %d unreasonable\r\n", LenuSensorID, *Lu8Distance);
            ret = 0;
        }
    }
#else
    // 原始严格检查
    else if ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_NULL)
            && ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_SW)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_HW)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_IN_BLIND)
                && (Gu8SensorErrSta[LenuSensorID] != ERR_STA_NOISE)))
    {
        DEBUG_SENSOR_ERROR("ValueUpdateDistance[%d] - INVALID: sensor error state=%d\r\n",
                           LenuSensorID, Gu8SensorErrSta[LenuSensorID]);
        ret = 0;
    }
#endif
    // 碎石路面滤波：在特定回波时间范围内通过电压参数过滤
    else if (
        (GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] >= gu16EchoTimex[0]
        && GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST] <= gu16EchoTimex[1])
        && (stEchoVoltage.mean <= VOLTAGE_THRESHOLD_AVERAGE)
       )
    {
        // 主发电压很大是正常障碍物，无需过滤
        if (GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_MASTER_BURST] <= VOLTAGE_THRESHOLD_AVERAGE * 2)
        {
            // 回波电压必须有一个正常
            if ((GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_MASTER_BURST] < NORMAL_VOLTAGE_THRESHOLD)
            && (GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[Su8ListenFlg[LenuSensorID]] < NORMAL_VOLTAGE_THRESHOLD)
            )
            {
                ret = 0;
            }
            // 回波电压都不能过小
            else if ((GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_MASTER_BURST] < MIN_VOLTAGE_THRESHOLD
            || GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[Su8ListenFlg[LenuSensorID]] < MIN_VOLTAGE_THRESHOLD)
            )
            {
                ret = 0;
            }
        }
    }

    DEBUG_VALUE("ValueUpdateDistance[%d] - returning %d\r\n", LenuSensorID, ret);
    return ret;
}

void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID)
{
    uint8_t ret = 0;
    uint8_t original_distance = Lu8Distance;

    DEBUG_FILTER("UpdateSnsDistance[%d] - input=%d, current=%d\r\n",
                 LenuSensorID, Lu8Distance, Gu8SysDistance[LenuSensorID]);

    if (Lu8Distance < FINAL_DISTANCE_LIMIT)
    {
        Lu8Distance = FINAL_DISTANCE_LIMIT;
        DEBUG_FILTER("UpdateSnsDistance[%d] - distance adjusted to FINAL_DISTANCE_LIMIT=%d\r\n",
                     LenuSensorID, FINAL_DISTANCE_LIMIT);
    }
    // 使用动态距离限制
    uint8_t max_distance = (LenuSensorID == SENSOR_ROL || LenuSensorID == SENSOR_ROR) ?
                          CORNER_FAR_WARN_DIS : CENTER_RAR_WARN_DIS;

    if (Lu8Distance <= max_distance)
    {
        Gu8SnsDisUpdateCnt[LenuSensorID][0] = 0;
        if ((Lu8Distance > Gu8SysDistance[LenuSensorID]))
        { 
            Gu8SnsDisUpdateCnt[LenuSensorID][2] = 0;
            Gu8SnsDisUpdateCnt[LenuSensorID][1]++;
            if ((Lu8Distance - Gu8SysDistance[LenuSensorID]) > 4)
            { 
                if (Gu8SnsDisUpdateCnt[LenuSensorID][1] > OBJ_FAR_CNT)
                {
                    DEBUG_FILTER("UpdateSnsDistance[%d] - FAR update: %d -> %d\r\n",
                                 LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
                    Gu8SysDistance[LenuSensorID] = Lu8Distance;
                    Gu8SnsDisUpdateCnt[LenuSensorID][1] = 0;
                }
#if ENABLE_FILTER_BYPASS
                else {
                    // 智能滤波绕过：如果距离合理，允许更新
                    if (Lu8Distance >= FILTER_BYPASS_MIN_DISTANCE && Lu8Distance <= FILTER_BYPASS_MAX_DISTANCE && Lu8Distance != NO_OBJECT) {
                        DEBUG_FILTER("UpdateSnsDistance[%d] - BYPASS FAR filter: %d -> %d (reasonable distance)\r\n",
                                     LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
                        Gu8SysDistance[LenuSensorID] = Lu8Distance;
                    } else {
                        DEBUG_FILTER("UpdateSnsDistance[%d] - FAR filter blocked: count=%d <= %d\r\n",
                                     LenuSensorID, Gu8SnsDisUpdateCnt[LenuSensorID][1], OBJ_FAR_CNT);
                    }
                }
#endif
            }
            else
            {
                Gu8SnsDisUpdateCnt[LenuSensorID][1] = 0;
            }
        }
        else
        { 
            Gu8SnsDisUpdateCnt[LenuSensorID][1] = 0;
            Gu8SnsDisUpdateCnt[LenuSensorID][2]++;
            ret = 0;
            if(Gu8SysDistance[LenuSensorID] == NO_OBJECT)
            {
                if(Lu8Distance <= 60) ret = 1;
            }
            if (Gu8SnsDisUpdateCnt[LenuSensorID][2] > Gu8CompareTime + ret)
            {
                DEBUG_FILTER("UpdateSnsDistance[%d] - NEAR update: %d -> %d\r\n",
                             LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
                Gu8SysDistance[LenuSensorID] = Lu8Distance;
                Gu8SnsDisUpdateCnt[LenuSensorID][2] = Gu8CompareTime-1;
            }
#if ENABLE_FILTER_BYPASS
            else {
                // 智能滤波绕过：如果距离合理，允许更新
                if (Lu8Distance >= FILTER_BYPASS_MIN_DISTANCE && Lu8Distance <= FILTER_BYPASS_MAX_DISTANCE && Lu8Distance != NO_OBJECT) {
                    DEBUG_FILTER("UpdateSnsDistance[%d] - BYPASS NEAR filter: %d -> %d (reasonable distance)\r\n",
                                 LenuSensorID, Gu8SysDistance[LenuSensorID], Lu8Distance);
                    Gu8SysDistance[LenuSensorID] = Lu8Distance;
                } else {
                    DEBUG_FILTER("UpdateSnsDistance[%d] - NEAR filter blocked: count=%d <= %d\r\n",
                                 LenuSensorID, Gu8SnsDisUpdateCnt[LenuSensorID][2], Gu8CompareTime + ret);
                }
            }
#endif
        }

        // 智能噪声抑制
#if ENABLE_NOISE_THRESHOLD_ADJUST
        uint8_t noise_threshold = NOISE_THRESHOLD_RELAXED; // 使用放宽的阈值
#else
        uint8_t noise_threshold = NOISE_THRESHOLD_DEFAULT; // 使用默认阈值
#endif

        if(stdevpx.mean > noise_threshold)
        {
            DEBUG_NOISE("UpdateSnsDistance[%d] - NOISE suppression: mean=%d > %d, set to NO_OBJECT\r\n",
                        LenuSensorID, (int)stdevpx.mean, noise_threshold);
            Gu8SysDistance[LenuSensorID] = NO_OBJECT;
        } else {
            DEBUG_NOISE("UpdateSnsDistance[%d] - NOISE check passed: mean=%d <= %d\r\n",
                        LenuSensorID, (int)stdevpx.mean, noise_threshold);
        }
    }
    else
    { 
        Gu8SnsDisUpdateCnt[LenuSensorID][1] = 0;
        Gu8SnsDisUpdateCnt[LenuSensorID][2] = 0;
        if (Gu8SysDistance[LenuSensorID] != NO_OBJECT)
        {
            if (++Gu8SnsDisUpdateCnt[LenuSensorID][0] > 4)
            {
                Gu8SysDistance[LenuSensorID] = NO_OBJECT;
            }
        }
    }
}

void CalcSnsObjDistance(tenu_SensorIDTypedef LenuSensorID)
{
    uint16_t Lu8SnsDistance[SENSOR_LISTEN_NUMBER] = {0};
    uint8_t Lu8SnsDistanceMin = 0xff, i, Lu8ListenFlag;
    uint16_t Lu16EchoTimeDelta;
    uint16_t Lu16TwiceDiffData = 0;
    uint8_t mat_diff_lis = 50;

    // 针对传感器1和3的详细调试
    uint8_t is_problematic_sensor = (LenuSensorID == 1 || LenuSensorID == 3);
    if (is_problematic_sensor) {
        printf("=== CalcSnsObjDistance[%d] START ===\r\n", LenuSensorID);
    }

    Lu8ListenFlag = 0;
    Lu8SnsDistance[SENSOR_MASTER_BURST] = INVALID_ECHO_TIME;
    Lu8SnsDistance[SENSOR_LISTEN_RIGHT] = INVALID_ECHO_TIME;
    Lu8SnsDistance[SENSOR_LISTEN_LEFT] = INVALID_ECHO_TIME;

    UpdateRingingTime(LenuSensorID);

    Lu16TwiceDiffData = (Gu8SpeedData <= 5) ? TWICE_DIS_VAL_DIFF_TIME5KM : TWICE_DIS_VAL_DIFF_TIME;

    // 智能回波选择：优先选择有效的回波
    uint8_t best_echo_index = SENSOR_MASTER_BURST;
    uint16_t best_echo_time = INVALID_ECHO_TIME;

    for (i = SENSOR_MASTER_BURST; i < SENSOR_LISTEN_NUMBER; i++)
    {
        Lu16EchoTimeDelta = abs(GstrSensorObjInfoBkp[LenuSensorID].u16EchoTime[i] - GstrSensorObjInfo[LenuSensorID].u16EchoTime[i]);

        DEBUG_DISTANCE("CalcSnsObjDistance[%d][%d] - EchoTime=%d, EchoTimeBkp=%d, Delta=%d, TwiceDiffData=%d\r\n",
                       LenuSensorID, i, GstrSensorObjInfo[LenuSensorID].u16EchoTime[i],
                       GstrSensorObjInfoBkp[LenuSensorID].u16EchoTime[i], Lu16EchoTimeDelta, Lu16TwiceDiffData);

#if ENABLE_FIRST_RUN_DETECTION
        // 优化：智能第一次运行检测
        uint8_t first_run = (GstrSensorObjInfoBkp[LenuSensorID].u16EchoTime[i] == 0);
        uint8_t delta_acceptable = (Lu16EchoTimeDelta < Lu16TwiceDiffData);
        uint8_t echo_time_valid = (GstrSensorObjInfo[LenuSensorID].u16EchoTime[i] < 20000 &&
                                   GstrSensorObjInfo[LenuSensorID].u16EchoTime[i] != INVALID_ECHO_TIME);

        // 智能条件：第一次运行或差值可接受，且回波时间有效
        if ((first_run || delta_acceptable) && echo_time_valid)
#else
        // 原始条件：严格的差值检查
        if ((Lu16EchoTimeDelta < Lu16TwiceDiffData) && (GstrSensorObjInfo[LenuSensorID].u16EchoTime[i] < 20000))
#endif
        {
            Lu8SnsDistance[i] = CalcTriangleDistance(GstrSensorObjInfo[LenuSensorID].u16EchoTime[i]);
            GstrSystemObjInfo.u16OriginDis[LenuSensorID][i] = Lu8SnsDistance[i];
            DEBUG_DISTANCE("CalcSnsObjDistance[%d][%d] - VALID: Distance=%d\r\n",
                           LenuSensorID, i, Lu8SnsDistance[i]);

            // 针对传感器1和3的详细调试
            if (is_problematic_sensor) {
                printf("STEP1[%d][%d]: EchoTime=%d -> Distance=%d, OriginDis=%d\r\n",
                       LenuSensorID, i, GstrSensorObjInfo[LenuSensorID].u16EchoTime[i],
                       Lu8SnsDistance[i], GstrSystemObjInfo.u16OriginDis[LenuSensorID][i]);
            }


        } else {
            GstrSystemObjInfo.u16OriginDis[LenuSensorID][i] = INVALID_ECHO_TIME;
            DEBUG_DISTANCE("CalcSnsObjDistance[%d][%d] - INVALID: Delta=%d >= %d OR EchoTime=%d >= 20000 (first_run=%d)\r\n",
                           LenuSensorID, i, Lu16EchoTimeDelta, Lu16TwiceDiffData, GstrSensorObjInfo[LenuSensorID].u16EchoTime[i], first_run);
        }
        GstrSensorObjInfoBkp[LenuSensorID].u16EchoTime[i] = GstrSensorObjInfo[LenuSensorID].u16EchoTime[i];
    }

    // 如果MASTER回波无效，使用最佳回波替代
    if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] == INVALID_ECHO_TIME &&
        best_echo_time != INVALID_ECHO_TIME) {
        GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] =
            CalcTriangleDistance(best_echo_time);
        DEBUG_CRITICAL("ECHO_FALLBACK[%d]: Using Echo[%d] Time=%d for MASTER (Distance=%d)\r\n",
                       LenuSensorID, best_echo_index, best_echo_time,
                       GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);
    }

    if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] != INVALID_ECHO_TIME &&
        GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT] != INVALID_ECHO_TIME)
    {
        if ((abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]) < mat_diff_lis) &&
            (abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT]) < mat_diff_lis))
        {
            Lu8ListenFlag = (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] < GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT]) ? SENSOR_LISTEN_LEFT : SENSOR_LISTEN_RIGHT;
        }
        else if (abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT]) < mat_diff_lis)
        {
            Lu8ListenFlag = SENSOR_LISTEN_RIGHT;
        }
        else if (abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]) < mat_diff_lis)
        {
            Lu8ListenFlag = SENSOR_LISTEN_LEFT;
        }
    }
    else if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] != INVALID_ECHO_TIME)
    {
        if (abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]) < mat_diff_lis)
        {
            Lu8ListenFlag = SENSOR_LISTEN_LEFT;
        }
    }
    else if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT] != INVALID_ECHO_TIME)
    {
        if (abs(GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] - GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT]) < mat_diff_lis)
        {
            Lu8ListenFlag = SENSOR_LISTEN_RIGHT;
        }
    }

    // 高级侦听回波验证
    if (Lu8ListenFlag != 0) {
        // 如果上一次发波只有主发，这次发波有侦听，判断侦听电压是否正常
        if (Su8ListenFlgBkp[LenuSensorID] == 0) {
            if (GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[Lu8ListenFlag] < MIN_VOLTAGE_THRESHOLD) {
                // 侦听电压不正常，去除侦听信号
                Lu8ListenFlag = (uint8_t)SENSOR_MASTER_BURST;
            }
        }

        // 侦听信号质量评估
        uint8_t listen_voltage = GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[Lu8ListenFlag];
        uint8_t master_voltage = GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_MASTER_BURST];

        // 如果侦听电压过低且主发电压正常，可能是误检
        if ((listen_voltage < MIN_VOLTAGE_THRESHOLD) && (master_voltage > NORMAL_VOLTAGE_THRESHOLD)) {
            Lu8ListenFlag = (uint8_t)SENSOR_MASTER_BURST;
        }

        // 智能侦听回波选择：如果两个侦听都有效，选择电压更强的
        if ((GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT] != INVALID_ECHO_TIME) &&
            (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT] != INVALID_ECHO_TIME)) {

            uint8_t left_voltage = GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_LISTEN_LEFT];
            uint8_t right_voltage = GstrSensorObjInfo[LenuSensorID].u8EchoVoltageLevel[SENSOR_LISTEN_RIGHT];

            // 选择电压更强且距离差更小的侦听
            if (left_voltage > right_voltage) {
                Lu8ListenFlag = SENSOR_LISTEN_LEFT;
            } else if (right_voltage > left_voltage) {
                Lu8ListenFlag = SENSOR_LISTEN_RIGHT;
            }
            // 如果电压相近，保持原有的距离差选择逻辑
        }
    }

    Su8ListenFlg[LenuSensorID] = Lu8ListenFlag;
    Su8ListenFlgBkp[LenuSensorID] = Lu8ListenFlag;
    GstrSystemObjInfo.u8ObjListenFlag[LenuSensorID] = Lu8ListenFlag;

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("ListenFlag[%d]: Final Lu8ListenFlag=%d, LEFT_dis=%d, RIGHT_dis=%d, MASTER_dis=%d\r\n",
               LenuSensorID, Lu8ListenFlag,
               GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_LEFT],
               GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_LISTEN_RIGHT],
               GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);
    }

    CalcObjCoord(LenuSensorID);

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("STEP2[%d]: After CalcObjCoord - Y=%d, X=%d, OriginDis[MASTER]=%d\r\n",
               LenuSensorID, GstrSystemObjInfo.s16ObjYcoord[LenuSensorID],
               GstrSystemObjInfo.s16ObjXcoord[LenuSensorID],
               GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);
    }

    // 强制时序稳定延迟 - 模拟调试输出的时序效应
#if !DEBUG_ENABLE
    // 仅在关闭调试时添加延迟，模拟调试输出的时序效应
    for(volatile int i = 0; i < 2000; i++) {
        __NOP(); // 空操作，约200μs延迟
    }
#endif

    // 关键调试：坐标计算结果
    DEBUG_CRITICAL("CoordCalc[%d]: Y=%d, X=%d, OriginDis[MASTER]=%d\r\n",
                   LenuSensorID, GstrSystemObjInfo.s16ObjYcoord[LenuSensorID],
                   GstrSystemObjInfo.s16ObjXcoord[LenuSensorID],
                   GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST]);

#if ENABLE_COORDINATE_BACKUP
    // 智能坐标计算备用方案
    if (GstrSystemObjInfo.s16ObjYcoord[LenuSensorID] == 0xff) {
        // 坐标计算失败，使用备用方案
        if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] != INVALID_ECHO_TIME) {
            Lu8SnsDistanceMin = (uint8_t)GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST];
            DEBUG_COORDINATE("CalcSnsObjDistance[%d] - Using MASTER distance directly: %d\r\n",
                             LenuSensorID, Lu8SnsDistanceMin);
            DEBUG_CRITICAL("DistanceSource[%d]: MASTER backup = %d\r\n", LenuSensorID, Lu8SnsDistanceMin);
        } else {
            // 最后的备用方案：直接从原始回波时间计算
            uint16_t raw_echo_time = GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST];
            DEBUG_CRITICAL("RAW_ECHO_CHECK[%d]: EchoTime=%d, INVALID=%d\r\n",
                           LenuSensorID, raw_echo_time, INVALID_ECHO_TIME);

            if (raw_echo_time != INVALID_ECHO_TIME && raw_echo_time > 0 && raw_echo_time < 20000) {
                Lu8SnsDistanceMin = raw_echo_time / 58; // 简单距离计算
                DEBUG_CRITICAL("DistanceSource[%d]: RAW_ECHO backup = %d (from EchoTime=%d)\r\n",
                               LenuSensorID, Lu8SnsDistanceMin, raw_echo_time);
            } else {
                // 最后的最后备用方案：使用历史有效距离
                if (Gu8SysDistance[LenuSensorID] != NO_OBJECT && Gu8SysDistance[LenuSensorID] > 0) {
                    Lu8SnsDistanceMin = Gu8SysDistance[LenuSensorID];
                    DEBUG_CRITICAL("DistanceSource[%d]: HISTORY backup = %d (keeping last valid)\r\n",
                                   LenuSensorID, Lu8SnsDistanceMin);
                } else {
                    // 如果历史数据也无效，使用固定的合理距离
                    Lu8SnsDistanceMin = 50; // 50cm作为默认距离
                    DEBUG_CRITICAL("DistanceSource[%d]: DEFAULT backup = %d (fixed fallback)\r\n",
                                   LenuSensorID, Lu8SnsDistanceMin);
                }
            }
        }
    } else if (GstrSystemObjInfo.s16ObjYcoord[LenuSensorID] == 0) {
        // 坐标Y=0的情况，使用主发距离作为备用
        if (GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST] != INVALID_ECHO_TIME) {
            Lu8SnsDistanceMin = (uint8_t)GstrSystemObjInfo.u16OriginDis[LenuSensorID][SENSOR_MASTER_BURST];
            DEBUG_COORDINATE("CalcSnsObjDistance[%d] - Y=0, using MASTER distance: %d\r\n",
                             LenuSensorID, Lu8SnsDistanceMin);
            DEBUG_CRITICAL("DistanceSource[%d]: Y=0 backup = %d\r\n", LenuSensorID, Lu8SnsDistanceMin);
        } else {
            // Y=0且主发距离无效时，也尝试原始回波时间
            uint16_t raw_echo_time = GstrSensorObjInfo[LenuSensorID].u16EchoTime[SENSOR_MASTER_BURST];
            DEBUG_CRITICAL("Y=0_RAW_ECHO_CHECK[%d]: EchoTime=%d, INVALID=%d\r\n",
                           LenuSensorID, raw_echo_time, INVALID_ECHO_TIME);

            if (raw_echo_time != INVALID_ECHO_TIME && raw_echo_time > 0 && raw_echo_time < 20000) {
                Lu8SnsDistanceMin = raw_echo_time / 58; // 简单距离计算
                DEBUG_CRITICAL("DistanceSource[%d]: Y=0 RAW_ECHO backup = %d (from EchoTime=%d)\r\n",
                               LenuSensorID, Lu8SnsDistanceMin, raw_echo_time);
            } else {
                // Y=0情况下的历史数据备用方案
                if (Gu8SysDistance[LenuSensorID] != NO_OBJECT && Gu8SysDistance[LenuSensorID] > 0) {
                    Lu8SnsDistanceMin = Gu8SysDistance[LenuSensorID];
                    DEBUG_CRITICAL("DistanceSource[%d]: Y=0 HISTORY backup = %d (keeping last valid)\r\n",
                                   LenuSensorID, Lu8SnsDistanceMin);
                } else {
                    Lu8SnsDistanceMin = 50; // 50cm作为默认距离
                    DEBUG_CRITICAL("DistanceSource[%d]: Y=0 DEFAULT backup = %d (fixed fallback)\r\n",
                                   LenuSensorID, Lu8SnsDistanceMin);
                }
            }
        }
    } else {
        Lu8SnsDistanceMin = (uint8_t)GstrSystemObjInfo.s16ObjYcoord[LenuSensorID];
        DEBUG_COORDINATE("CalcSnsObjDistance[%d] - Using coordinate Y: %d\r\n",
                         LenuSensorID, Lu8SnsDistanceMin);
        DEBUG_CRITICAL("DistanceSource[%d]: Coordinate Y = %d\r\n", LenuSensorID, Lu8SnsDistanceMin);
    }
#else
    // 原始逻辑：直接使用坐标Y值
    Lu8SnsDistanceMin = (uint8_t)GstrSystemObjInfo.s16ObjYcoord[LenuSensorID];
    DEBUG_CRITICAL("DistanceSource[%d]: Direct Y = %d\r\n", LenuSensorID, Lu8SnsDistanceMin);

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("STEP3[%d]: Using Direct Y = %d (from ObjYcoord=%d)\r\n",
               LenuSensorID, Lu8SnsDistanceMin, GstrSystemObjInfo.s16ObjYcoord[LenuSensorID]);
    }
#endif
    
    if (LenuSensorID == SENSOR_MASTER)
    {
        GetEchoVoltageInfo();
    }
    // 智能距离验证和处理
    uint8_t value_check_result = ValueUpdateDistance(&Lu8SnsDistanceMin, LenuSensorID);
    DEBUG_CRITICAL("ValueUpdateDistance[%d] result=%d, distance=%d\r\n", LenuSensorID, value_check_result, Lu8SnsDistanceMin);

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("STEP4[%d]: ValueUpdateDistance result=%d, distance=%d\r\n",
               LenuSensorID, value_check_result, Lu8SnsDistanceMin);
    }

    if (value_check_result == 0x00)
    {
#if ENABLE_DISTANCE_PRESERVATION
        // 距离保护模式：如果距离在合理范围内，保持距离数据
        if (Lu8SnsDistanceMin >= FILTER_BYPASS_MIN_DISTANCE && Lu8SnsDistanceMin <= FILTER_BYPASS_MAX_DISTANCE) {
            DEBUG_VALUE("ValueUpdateDistance failed, but keeping reasonable distance: %d\r\n", Lu8SnsDistanceMin);
            DEBUG_CRITICAL("DISTANCE_PRESERVATION: keeping distance %d\r\n", Lu8SnsDistanceMin);
            // 保持计算出的距离，不设置为NO_OBJECT
        } else {
            DEBUG_VALUE("ValueUpdateDistance failed, distance %d unreasonable, setting to NO_OBJECT\r\n", Lu8SnsDistanceMin);
            DEBUG_CRITICAL("DISTANCE_PRESERVATION: clearing distance %d -> NO_OBJECT\r\n", Lu8SnsDistanceMin);
            Lu8SnsDistanceMin = NO_OBJECT;
        }
#else
        // 原始严格模式：验证失败直接设置为NO_OBJECT
        DEBUG_CRITICAL("STRICT_MODE: clearing distance %d -> NO_OBJECT\r\n", Lu8SnsDistanceMin);
        Lu8SnsDistanceMin = NO_OBJECT;
#endif
    }

    // 距离计算过程调试
    DEBUG_DISTANCE("CalcSnsObjDistance[%d] - Lu8SnsDistanceMin=%d, before update Gu8SysDistance=%d\r\n",
                   LenuSensorID, Lu8SnsDistanceMin, Gu8SysDistance[LenuSensorID]);

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("STEP5[%d]: Before UpdateSnsDistance - Lu8SnsDistanceMin=%d, Gu8SysDistance=%d\r\n",
               LenuSensorID, Lu8SnsDistanceMin, Gu8SysDistance[LenuSensorID]);
    }

    UpdateSnsDistance(Lu8SnsDistanceMin, LenuSensorID);

    // 针对传感器1和3的详细调试
    if (is_problematic_sensor) {
        printf("STEP6[%d]: After UpdateSnsDistance - Gu8SysDistance=%d\r\n",
               LenuSensorID, Gu8SysDistance[LenuSensorID]);
        printf("=== CalcSnsObjDistance[%d] END ===\r\n", LenuSensorID);
    }

    // 更新结果调试
    DEBUG_DISTANCE("CalcSnsObjDistance[%d] - after update Gu8SysDistance=%d\r\n",
                   LenuSensorID, Gu8SysDistance[LenuSensorID]);

    // 关键系统信息 - 始终输出
    DEBUG_CRITICAL("Sensor[%d] Final: Distance=%dcm, ErrSta=%d\r\n",
                   LenuSensorID, Gu8SysDistance[LenuSensorID], Gu8SensorErrSta[LenuSensorID]);

    // 执行各种错误检测
    SnsBlockageErrorCheck(LenuSensorID);
    SnsOverTemperationErrorCheck(LenuSensorID);
    SnsFrequencyErrorCheck(LenuSensorID);
    SnsRingErrorCheck(LenuSensorID);
    SnsModbusComErrorCheck(LenuSensorID);

    // 盲区检测
    Echo_Private_InBlind(LenuSensorID);

    // 综合错误状态检查 - 但不清空距离数据
    SensorErrorCheck(LenuSensorID);

    // 计算警告分段
    CalcWarningZone(LenuSensorID, Gu8SysDistance[LenuSensorID]);
}
