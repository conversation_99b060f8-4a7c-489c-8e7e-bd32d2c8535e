/**
 * @file robot_integration_test.c
 * @brief 机器人8传感器系统集成测试程序
 * @date 2024-07-08
 */

#include "robot_ultrasonic.h"
#include "echo_processing.h"
#include "modbus_interface.h"
#include <stdio.h>

/**
 * @brief 测试8传感器系统基本功能
 */
void Robot_BasicFunctionTest(void)
{
    printf("=== Robot 8-Sensor System Basic Test ===\r\n");
    
    // 1. 测试传感器数量配置
    printf("SENSOR_NUMBER = %d (Expected: 8)\r\n", SENSOR_NUMBER);
    if(SENSOR_NUMBER != 8) {
        printf("ERROR: SENSOR_NUMBER should be 8!\r\n");
        return;
    }
    
    // 2. 测试传感器ID枚举
    printf("Testing Sensor ID Enumeration:\r\n");
    printf("  SENSOR_F1 = %d\r\n", SENSOR_F1);
    printf("  SENSOR_F2 = %d\r\n", SENSOR_F2);
    printf("  SENSOR_R1 = %d\r\n", SENSOR_R1);
    printf("  SENSOR_R2 = %d\r\n", SENSOR_R2);
    printf("  SENSOR_B1 = %d\r\n", SENSOR_B1);
    printf("  SENSOR_B2 = %d\r\n", SENSOR_B2);
    printf("  SENSOR_L1 = %d\r\n", SENSOR_L1);
    printf("  SENSOR_L2 = %d\r\n", SENSOR_L2);
    
    // 3. 测试全局数组大小
    printf("Testing Global Arrays:\r\n");
    printf("  Gu8SysDistance array size: %d bytes\r\n", sizeof(Gu8SysDistance));
    printf("  GstrSensorObjInfo array size: %d bytes\r\n", sizeof(GstrSensorObjInfo));
    
    // 4. 测试机器人方向枚举
    printf("Testing Robot Direction Enumeration:\r\n");
    printf("  ROBOT_DIRECTION_FORWARD = %d\r\n", ROBOT_DIRECTION_FORWARD);
    printf("  ROBOT_DIRECTION_RIGHT = %d\r\n", ROBOT_DIRECTION_RIGHT);
    printf("  ROBOT_DIRECTION_BACKWARD = %d\r\n", ROBOT_DIRECTION_BACKWARD);
    printf("  ROBOT_DIRECTION_LEFT = %d\r\n", ROBOT_DIRECTION_LEFT);
    
    printf("Basic Function Test Completed!\r\n");
}

/**
 * @brief 测试发波模式配置
 */
void Robot_BurstPatternTest(void)
{
    printf("=== Robot Burst Pattern Test ===\r\n");
    
    // 测试方向发波模式
    uint16_t direction_patterns[] = {
        0x03,  // 前方: F1+F2 (bit0+bit1)
        0x0C,  // 右侧: R1+R2 (bit2+bit3)  
        0x30,  // 后方: B1+B2 (bit4+bit5)
        0xC0   // 左侧: L1+L2 (bit6+bit7)
    };
    
    const char* direction_names[] = {"Forward", "Right", "Backward", "Left"};
    
    for(uint8_t i = 0; i < 4; i++) {
        printf("%s Direction Pattern: 0x%02X\r\n", direction_names[i], direction_patterns[i]);
        
        // 分析哪些传感器会被激活
        printf("  Active sensors: ");
        for(uint8_t j = 0; j < 8; j++) {
            if(direction_patterns[i] & (0x01 << j)) {
                printf("S%d ", j+1);
            }
        }
        printf("\r\n");
    }
    
    // 测试单传感器发波模式
    printf("\nSingle Sensor Patterns:\r\n");
    for(uint8_t i = 0; i < 8; i++) {
        uint16_t pattern = 0x01 << i;
        printf("  Sensor %d: 0x%02X\r\n", i+1, pattern);
    }
    
    printf("Burst Pattern Test Completed!\r\n");
}

/**
 * @brief 测试数据结构初始化
 */
void Robot_DataStructureTest(void)
{
    printf("=== Robot Data Structure Test ===\r\n");
    
    // 初始化机器人系统
    Robot_UltrasonicInit();
    
    // 检查初始化结果
    printf("Robot Configuration:\r\n");
    printf("  Min Safe Distance: %d mm\r\n", GstrRobotConfig.u16MinSafeDistance);
    printf("  Max Detect Distance: %d mm\r\n", GstrRobotConfig.u16MaxDetectDistance);
    printf("  Update Frequency: %d Hz\r\n", GstrRobotConfig.u8UpdateFrequency);
    printf("  Motion State: %d\r\n", GstrRobotConfig.eMotionState);
    printf("  Adaptive Mode: %s\r\n", GstrRobotConfig.u8EnableAdaptiveMode ? "Enabled" : "Disabled");
    
    // 检查方向信息初始化
    printf("Direction Information:\r\n");
    for(uint8_t i = 0; i < ROBOT_DIRECTION_NUMBER; i++) {
        printf("  Direction %d: Distance=%d, Confidence=%d%%, Warning=%d\r\n",
               i, GstrRobotDirectionInfo.u16Distance[i],
               GstrRobotDirectionInfo.u8Confidence[i],
               GstrRobotDirectionInfo.u8WarningLevel[i]);
    }
    
    printf("Data Structure Test Completed!\r\n");
}

/**
 * @brief 测试API函数
 */
void Robot_APITest(void)
{
    printf("=== Robot API Test ===\r\n");
    
    // 测试运动状态设置和获取
    printf("Testing Motion State API:\r\n");
    Robot_SetMotionState(ROBOT_MOTION_FORWARD);
    tenu_RobotMotionStateTypedef state = Robot_GetMotionState();
    printf("  Set FORWARD, Get: %d\r\n", state);
    
    Robot_SetMotionState(ROBOT_MOTION_STOP);
    state = Robot_GetMotionState();
    printf("  Set STOP, Get: %d\r\n", state);
    
    // 测试距离获取API
    printf("Testing Distance API:\r\n");
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint16_t distance = Robot_GetDirectionDistance(dir);
        printf("  Direction %d Distance: %d mm\r\n", dir, distance);
    }
    
    // 测试警告等级API
    printf("Testing Warning Level API:\r\n");
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        tenu_RobotWarningLevelTypedef warning = Robot_GetDirectionWarningLevel(dir);
        printf("  Direction %d Warning: %d\r\n", dir, warning);
    }
    
    // 测试安全检查API
    printf("Testing Safety Check API:\r\n");
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint8_t safe = Robot_IsSafeToMove(dir);
        printf("  Direction %d Safe: %s\r\n", dir, safe ? "Yes" : "No");
    }
    
    printf("API Test Completed!\r\n");
}

/**
 * @brief 测试发波策略选择
 */
void Robot_BurstStrategyTest(void)
{
    printf("=== Robot Burst Strategy Test ===\r\n");
    
    // 测试不同运动状态下的发波策略
    tenu_RobotMotionStateTypedef motion_states[] = {
        ROBOT_MOTION_STOP,
        ROBOT_MOTION_FORWARD,
        ROBOT_MOTION_BACKWARD,
        ROBOT_MOTION_TURN_LEFT,
        ROBOT_MOTION_TURN_RIGHT
    };
    
    const char* state_names[] = {
        "STOP", "FORWARD", "BACKWARD", "TURN_LEFT", "TURN_RIGHT"
    };
    
    for(uint8_t state_idx = 0; state_idx < 5; state_idx++) {
        printf("Motion State: %s\r\n", state_names[state_idx]);
        
        for(uint8_t dir = 0; dir < 4; dir++) {
            uint16_t pattern = Robot_SelectBurstPattern(motion_states[state_idx], dir);
            printf("  Direction %d: Pattern 0x%02X\r\n", dir, pattern);
        }
        printf("\r\n");
    }
    
    printf("Burst Strategy Test Completed!\r\n");
}

/**
 * @brief 测试自适应延时
 */
void Robot_AdaptiveDelayTest(void)
{
    printf("=== Robot Adaptive Delay Test ===\r\n");
    
    tenu_RobotMotionStateTypedef motion_states[] = {
        ROBOT_MOTION_STOP,
        ROBOT_MOTION_FORWARD,
        ROBOT_MOTION_BACKWARD,
        ROBOT_MOTION_TURN_LEFT,
        ROBOT_MOTION_TURN_RIGHT
    };
    
    const char* state_names[] = {
        "STOP", "FORWARD", "BACKWARD", "TURN_LEFT", "TURN_RIGHT"
    };
    
    for(uint8_t i = 0; i < 5; i++) {
        uint16_t delay = Robot_GetAdaptiveDelay(motion_states[i]);
        printf("  %s: %d ms\r\n", state_names[i], delay);
    }
    
    printf("Adaptive Delay Test Completed!\r\n");
}

/**
 * @brief 运行完整的集成测试
 */
void Robot_RunIntegrationTest(void)
{
    printf("\r\n");
    printf("*************************************************\r\n");
    printf("*    Robot 8-Sensor System Integration Test    *\r\n");
    printf("*************************************************\r\n");
    printf("\r\n");
    
    // 运行各项测试
    Robot_BasicFunctionTest();
    printf("\r\n");
    
    Robot_BurstPatternTest();
    printf("\r\n");
    
    Robot_DataStructureTest();
    printf("\r\n");
    
    Robot_APITest();
    printf("\r\n");
    
    Robot_BurstStrategyTest();
    printf("\r\n");
    
    Robot_AdaptiveDelayTest();
    printf("\r\n");
    
    printf("*************************************************\r\n");
    printf("*         Integration Test Completed!          *\r\n");
    printf("*************************************************\r\n");
    printf("\r\n");
}

/**
 * @brief 简单的集成测试任务
 */
void Robot_IntegrationTestTask(void)
{
    // 等待系统初始化完成
    osDelay(1000);
    
    // 运行集成测试
    Robot_RunIntegrationTest();
    
    // 测试完成后删除任务
    printf("Integration test task completed, deleting task...\r\n");
    osThreadTerminate(NULL);
}
