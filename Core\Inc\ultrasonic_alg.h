/******************************************************************************
 * Ultrasonic Algorithm Common Definitions
 * Ported from Genie02_E52436 project
 *****************************************************************************/
#ifndef ULTRASONIC_ALG_H
#define ULTRASONIC_ALG_H

#include <stdint.h>
#include "modbus_interface.h"
#include "echo_processing.h"




// 温度补偿参数
#define TEMP_CORRECTION_FACTOR 6  // 温度补偿系数(0.06m/s/°C)
#define BASE_SOUND_SPEED 3314     // 基准声速(331.4m/s)

// 核心算法函数声明
void UltrasonicAlg_Init(void);
void UltrasonicAlg_ProcessData(SensorEchoInfoTypedef* data, tenu_SensorIDTypedef sensor_id);

// 三角测量函数
void Triangulation_CalculatePosition(SensorEchoInfoTypedef* master,
                                   SensorEchoInfoTypedef* slave, 
                                   float* x, float* y);

#endif /* ULTRASONIC_ALG_H */
