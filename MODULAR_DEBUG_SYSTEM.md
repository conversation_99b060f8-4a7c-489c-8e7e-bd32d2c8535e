# 模块化调试系统配置

## 🎉 **问题已解决！传感器1和3恢复正常工作**

从最新的日志可以确认，传感器1和3现在已经能够：

### ✅ **成功的处理流程**
```
STEP1[1][0]: EchoTime=2793 -> Distance=48, OriginDis=48  ✅ 回波计算成功
E52436_DECISION[1]: MASTER=48, LEFT=65535, RIGHT=60, threshold=200 -> ListenFlag=1  ✅ 决策成功
STEP2[1]: After CalcObjCoord - Y=48, X=0, OriginDis[MASTER]=48  ✅ 坐标计算成功
ValueUpdate[1]: input=48, ErrSta=7  ✅ 过温状态被容忍
ValueUpdate[1]: FINAL result=1, distance=48  ✅ 验证通过
STEP4[1]: ValueUpdateDistance result=1, distance=48  ✅ 距离有效
```

## 🔧 **已创建的模块化调试系统**

### **通讯模块调试开关**
```c
#define DEBUG_MODBUS_COMMUNICATION      0   // Modbus通信调试
#define DEBUG_MODBUS_RAW_DATA           0   // 原始Modbus数据调试
#define DEBUG_MODBUS_PARSED_DATA        0   // 解析后数据调试
#define DEBUG_MODBUS_ECHO_DATA          0   // 回波数据调试
```

### **算法模块调试开关**
```c
#define DEBUG_ALGORITHM_PROCESSING      0   // 算法处理调试
#define DEBUG_E52436_DECISION           0   // E52436决策逻辑调试
#define DEBUG_DISTANCE_CALCULATION      0   // 距离计算调试
#define DEBUG_COORDINATE_CALCULATION    0   // 坐标计算调试
#define DEBUG_VALUE_UPDATE              0   // 数值更新调试
#define DEBUG_FILTER_ALGORITHM          0   // 滤波算法调试
```

### **系统状态调试开关**
```c
#define DEBUG_SENSOR_ERROR_STATE        0   // 传感器错误状态调试
#define DEBUG_NOISE_SUPPRESSION         0   // 噪声抑制调试
#define DEBUG_ROBOT_DIRECTION_FUSION    0   // 机器人方向融合调试
```

## 📊 **调试宏定义**

### **通讯模块调试宏**
```c
#if DEBUG_MODBUS_COMMUNICATION
#define DEBUG_MODBUS(fmt, ...) DEBUG_PRINT("MODBUS_DEBUG: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS(fmt, ...)
#endif

#if DEBUG_MODBUS_RAW_DATA
#define DEBUG_MODBUS_RAW(fmt, ...) DEBUG_PRINT("RAW_MODBUS_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_RAW(fmt, ...)
#endif

#if DEBUG_MODBUS_PARSED_DATA
#define DEBUG_MODBUS_PARSED(fmt, ...) DEBUG_PRINT("PARSED_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_PARSED(fmt, ...)
#endif

#if DEBUG_MODBUS_ECHO_DATA
#define DEBUG_MODBUS_ECHO(fmt, ...) DEBUG_PRINT("ECHO_DATA: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_MODBUS_ECHO(fmt, ...)
#endif
```

### **算法模块调试宏**
```c
#if DEBUG_ALGORITHM_PROCESSING
#define DEBUG_ALGORITHM(fmt, ...) DEBUG_PRINT("ALGORITHM: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_ALGORITHM(fmt, ...)
#endif

#if DEBUG_E52436_DECISION
#define DEBUG_E52436(fmt, ...) DEBUG_PRINT("E52436_DECISION: " fmt, ##__VA_ARGS__)
#else
#define DEBUG_E52436(fmt, ...)
#endif
```

## 🎯 **使用方法**

### **调试通讯问题**
```c
// 在debug_config.h中设置：
#define DEBUG_MODBUS_COMMUNICATION      1   // 启用Modbus通信调试
#define DEBUG_MODBUS_RAW_DATA           1   // 启用原始数据调试
#define DEBUG_MODBUS_PARSED_DATA        1   // 启用解析数据调试
#define DEBUG_MODBUS_ECHO_DATA          1   // 启用回波数据调试
```

**预期输出**：
```
MODBUS_DEBUG: QUERY[Sensor1]: addr=2, func=0x03, start_reg=18, reg_count=16
MODBUS_DEBUG: Sensor1(addr=2) retry=0, result=FAIL
MODBUS_DEBUG: Sensor1(addr=2) retry=1, result=OK
RAW_MODBUS_DATA: [Sensor1]: reg[0]=3356 reg[1]=0 reg[2]=2 reg[3]=2806 ...
PARSED_DATA: [Sensor1]: RingTime=3356, Freq=0, Noise=2, Status=0xF6
ECHO_DATA: [Sensor1]: Echo[0]: Time=2806, Width=188, Volt=62, Conf=14 | ...
```

### **调试算法问题**
```c
// 在debug_config.h中设置：
#define DEBUG_ALGORITHM_PROCESSING      1   // 启用算法处理调试
#define DEBUG_E52436_DECISION           1   // 启用E52436决策调试
#define DEBUG_DISTANCE_CALCULATION      1   // 启用距离计算调试
#define DEBUG_COORDINATE_CALCULATION    1   // 启用坐标计算调试
#define DEBUG_VALUE_UPDATE              1   // 启用数值更新调试
```

**预期输出**：
```
ALGORITHM: === CalcSnsObjDistance[1] START ===
ALGORITHM: STEP1[1][0]: EchoTime=2806 -> Distance=48, OriginDis=48
E52436_DECISION: [1]: MASTER=48, LEFT=65535, RIGHT=60, threshold=200 -> ListenFlag=1
COORDINATE: CalcObjCoord[1]: ListenFlag=1, LfCosM=0.000000, Lu16SinR=1.000000
ALGORITHM: STEP2[1]: After CalcObjCoord - Y=48, X=0, OriginDis[MASTER]=48
VALUE_UPDATE: [1]: input=48, ErrSta=7, result=1, distance=48
```

### **调试系统状态问题**
```c
// 在debug_config.h中设置：
#define DEBUG_SENSOR_ERROR_STATE        1   // 启用传感器错误调试
#define DEBUG_NOISE_SUPPRESSION         1   // 启用噪声处理调试
#define DEBUG_ROBOT_DIRECTION_FUSION    1   // 启用机器人状态调试
```

## 🔧 **已更新的代码文件**

### **1. debug_config.h**
- ✅ 添加了模块化调试开关
- ✅ 定义了对应的调试宏
- ✅ 保持了向后兼容性

### **2. modbus_interface.c**
- ✅ 更新了Modbus通信调试输出
- ✅ 使用DEBUG_MODBUS宏
- ✅ 使用DEBUG_MODBUS_RAW宏
- ✅ 使用DEBUG_MODBUS_PARSED宏
- ✅ 使用DEBUG_MODBUS_ECHO宏

### **3. echo_processing.c**
- ✅ 更新了算法处理调试输出
- ✅ 使用DEBUG_ALGORITHM宏
- ✅ 保留了关键的步骤调试信息

## 📋 **调试开关使用指南**

### **通讯问题诊断**
```c
// 只启用通讯相关调试
#define DEBUG_MODBUS_COMMUNICATION      1
#define DEBUG_MODBUS_RAW_DATA           1
#define DEBUG_MODBUS_PARSED_DATA        1
#define DEBUG_MODBUS_ECHO_DATA          1

// 关闭算法调试
#define DEBUG_ALGORITHM_PROCESSING      0
#define DEBUG_E52436_DECISION           0
// ... 其他算法调试开关设为0
```

### **算法问题诊断**
```c
// 关闭通讯调试
#define DEBUG_MODBUS_COMMUNICATION      0
#define DEBUG_MODBUS_RAW_DATA           0
// ... 其他通讯调试开关设为0

// 只启用算法相关调试
#define DEBUG_ALGORITHM_PROCESSING      1
#define DEBUG_E52436_DECISION           1
#define DEBUG_DISTANCE_CALCULATION      1
#define DEBUG_COORDINATE_CALCULATION    1
#define DEBUG_VALUE_UPDATE              1
```

### **系统状态诊断**
```c
// 关闭通讯和算法调试
// ... 设为0

// 只启用系统状态调试
#define DEBUG_SENSOR_ERROR_STATE        1
#define DEBUG_NOISE_SUPPRESSION         1
#define DEBUG_ROBOT_DIRECTION_FUSION    1
```

## 🎯 **优势**

### **1. 模块化**
- 可以独立调试不同模块
- 避免调试信息过多导致的混乱
- 提高调试效率

### **2. 可配置**
- 根据需要启用特定的调试输出
- 减少不必要的调试信息
- 保持系统性能

### **3. 可维护**
- 清晰的调试分类
- 统一的调试宏命名
- 易于扩展和修改

### **4. 向后兼容**
- 保留了原有的调试功能
- 不影响现有代码
- 平滑过渡到新系统

## 🎉 **总结**

现在您有了一个完整的模块化调试系统：

1. **✅ 问题已解决** - 传感器1和3恢复正常工作
2. **✅ 调试系统完善** - 按模块分类的调试开关
3. **✅ 使用简单** - 只需修改debug_config.h中的开关
4. **✅ 功能强大** - 可以精确控制调试输出

**现在您可以根据需要调试通讯或算法，系统已经完全可用！** 🎉
