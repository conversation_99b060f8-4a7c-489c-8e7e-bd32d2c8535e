# 回归原始E52436移植

## 🎯 **立即执行：回归原始移植**

您的观察完全正确。我们在解决传感器1和3问题的过程中，确实偏离了原始E52436移植方案太多。现在立即回归原始设计。

## 🔧 **已执行的回归操作**

### **1. 禁用所有"智能优化"开关**
```c
// 滤波算法优化开关 - 回归原始E52436移植
#define ENABLE_ADAPTIVE_FILTERING       0   // 禁用自适应滤波
#define ENABLE_FIRST_RUN_DETECTION      0   // 禁用第一次运行检测
#define ENABLE_COORDINATE_BACKUP        0   // 禁用坐标计算备用方案
#define ENABLE_NOISE_THRESHOLD_ADJUST   0   // 禁用噪声阈值调整
#define ENABLE_FILTER_BYPASS           0   // 禁用滤波绕过

// 传感器错误处理优化 - 回归原始E52436移植
#define ENABLE_SENSOR_ERROR_TOLERANCE   0   // 禁用传感器错误容错
#define ENABLE_DISTANCE_PRESERVATION    0   // 禁用距离数据保护
```

## 📊 **回归后的系统行为**

### **CalcSnsObjDistance() - 恢复原始逻辑**
```c
void CalcSnsObjDistance(tenu_SensorIDTypedef LenuSensorID) {
    // ✅ 原始E52436逻辑
    for (i = SENSOR_MASTER_BURST; i < SENSOR_LISTEN_NUMBER; i++) {
        Lu16EchoTimeDelta = abs(backup_time - current_time);
        
        // 原始条件：严格的差值检查
        if ((Lu16EchoTimeDelta < Lu16TwiceDiffData) && 
            (GstrSensorObjInfo[LenuSensorID].u16EchoTime[i] < 20000)) {
            Lu8SnsDistance[i] = CalcTriangleDistance(echo_time);
            GstrSystemObjInfo.u16OriginDis[LenuSensorID][i] = Lu8SnsDistance[i];
        } else {
            GstrSystemObjInfo.u16OriginDis[LenuSensorID][i] = INVALID_ECHO_TIME;
        }
    }
    
    // 调用坐标计算
    CalcObjCoord(LenuSensorID);
    
    // 原始逻辑：直接使用坐标Y值
    Lu8SnsDistanceMin = (uint8_t)GstrSystemObjInfo.s16ObjYcoord[LenuSensorID];
    
    // 原始距离验证
    uint8_t value_check_result = ValueUpdateDistance(&Lu8SnsDistanceMin, LenuSensorID);
    if (value_check_result == 0x00) {
        // 原始严格模式：验证失败直接设置为NO_OBJECT
        Lu8SnsDistanceMin = NO_OBJECT;
    }
    
    // 调用原始滤波算法
    UpdateSnsDistance(Lu8SnsDistanceMin, LenuSensorID);
}
```

### **ValueUpdateDistance() - 恢复原始逻辑**
```c
uint8_t ValueUpdateDistance(uint8_t *Lu8Distance, tenu_SensorIDTypedef LenuSensorID) {
    // ✅ 原始E52436逻辑
    
    // 原始严格检查：有错误就拒绝
    if ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_NULL) &&
        ((Gu8SensorErrSta[LenuSensorID] != ERR_STA_SW) &&
         (Gu8SensorErrSta[LenuSensorID] != ERR_STA_HW) &&
         (Gu8SensorErrSta[LenuSensorID] != ERR_STA_IN_BLIND) &&
         (Gu8SensorErrSta[LenuSensorID] != ERR_STA_NOISE))) {
        return 0; // 严格拒绝
    }
    
    // 碎石路面滤波：原始E52436算法
    if ((echo_time >= gu16EchoTimex[0] && echo_time <= gu16EchoTimex[1]) &&
        (stEchoVoltage.mean <= VOLTAGE_THRESHOLD_AVERAGE)) {
        return 0; // 电压过低，拒绝
    }
    
    return 1; // 通过验证
}
```

### **UpdateSnsDistance() - 保持原始逻辑**
```c
void UpdateSnsDistance(uint8_t Lu8Distance, tenu_SensorIDTypedef LenuSensorID) {
    // ✅ 原始E52436逻辑保持不变
    
    // 距离增大：需要多次确认 (OBJ_FAR_CNT)
    if (Lu8Distance > Gu8SysDistance[LenuSensorID]) {
        if (Gu8SnsDisUpdateCnt[LenuSensorID][1] > OBJ_FAR_CNT) {
            Gu8SysDistance[LenuSensorID] = Lu8Distance;
        }
    }
    // 距离减小：快速响应 (Gu8CompareTime)
    else {
        if (Gu8SnsDisUpdateCnt[LenuSensorID][2] > Gu8CompareTime + ret) {
            Gu8SysDistance[LenuSensorID] = Lu8Distance;
        }
    }
    
    // 原始噪声抑制
    if(stdevpx.mean > NOISE_THRESHOLD_DEFAULT) {
        Gu8SysDistance[LenuSensorID] = NO_OBJECT;
    }
}
```

## 🔍 **现在专注解决根本问题**

### **基于原始E52436设计理念，重新分析传感器1和3问题**

#### **1. E52436主发/从收机制**
```
原始设计：
- 传感器按组工作：主发 + 相邻从收
- 主发传感器：发射超声波，接收直接回波
- 从收传感器：接收相邻主发的间接回波
- 三角定位：通过主发和从收的时间差计算精确位置
```

#### **2. 可能的配置问题**
```
传感器1: Freq=0  → 从收模式，但可能配置错误
传感器3: Freq=58 → 主发模式，但相邻传感器配置可能不匹配
```

#### **3. 发波控制问题**
```
当前发波模式可能不匹配E52436的协同工作要求：
- 发波时序不正确
- 相邻传感器未正确配置为从收模式
- 主发/从收的协调机制缺失
```

## 📋 **下一步行动计划**

### **1. 立即测试原始移植**
```
重新编译并运行，观察：
- 传感器1和3在原始E52436逻辑下的表现
- 是否仍然出现坐标计算失败 (Y=255, X=255)
- 原始回波时间是否有效
```

### **2. 检查传感器配置**
```
基于E52436原始设计，检查：
- 传感器主发/从收模式配置
- 发波控制命令是否正确
- 相邻传感器的协同工作设置
```

### **3. 优化Modbus时序**
```
基于E52436原始时序要求：
- 发波命令与数据读取的间隔
- 多传感器并发访问的时序
- 传感器数据准备时间
```

### **4. 硬件层面检查**
```
如果软件层面无法解决：
- 检查传感器安装和连接
- 验证电源和信号完整性
- 确认E52436硬件配置
```

## 🎯 **目标**

**恢复E52436原始算法的纯净性，然后基于硬件特性和原始设计理念解决传感器1和3的问题，而不是通过软件补丁绕过问题。**

## 📊 **预期效果**

回归原始移植后：

### **如果问题仍然存在**
说明问题确实在硬件配置或E52436特定的工作机制上，需要：
- 深入理解E52436的主发/从收协同机制
- 正确配置传感器的工作模式
- 优化发波控制和数据读取时序

### **如果问题得到改善**
说明我们之前的"智能优化"确实干扰了原始算法，应该：
- 保持原始移植的纯净性
- 只在确实必要时进行最小化修改
- 基于E52436原始设计理念进行优化

## 💡 **核心理念**

**尊重原始E52436的设计智慧，相信经过实际验证的算法，专注解决配置和时序问题，而不是重新发明轮子。**

现在请重新编译并测试，让我们看看原始E52436移植在传感器1和3上的真实表现！
