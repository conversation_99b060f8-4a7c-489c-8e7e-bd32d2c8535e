/**
 * @file robot_ultrasonic.c
 * @brief 机器人底盘8传感器超声波测距系统实现
 * @date 2024-07-08
 */

#include "robot_ultrasonic.h"
#include "cmsis_os.h"
#include <string.h>
#include <stdio.h>
#include "modbus_interface.h"
#include "echo_processing.h"
#include "debug_config.h"

// 外部变量声明
extern modbusHandler_t ModbusH2;

// 距离阈值定义
#define ROBOT_SAFE_DISTANCE      2000  // 2m - 安全距离
#define ROBOT_CAUTION_DISTANCE   1000  // 1m - 注意距离  
#define ROBOT_DANGER_DISTANCE    500   // 0.5m - 危险距离
#define ROBOT_CRITICAL_DISTANCE  200   // 0.2m - 紧急距离

// 更新频率定义
#define ROBOT_FREQ_STOP          5     // 停止时5Hz
#define ROBOT_FREQ_SLOW          10    // 慢速时10Hz
#define ROBOT_FREQ_NORMAL        20    // 正常时20Hz
#define ROBOT_FREQ_FAST          50    // 快速时50Hz

// 全局变量定义
RobotDirectionInfoTypedef GstrRobotDirectionInfo;
RobotConfigTypedef GstrRobotConfig;
SingleSensorObjectInfo GstrRobotSensorInfo[ROBOT_SENSOR_NUMBER];
uint8_t Gu8RobotSysDistance[ROBOT_SENSOR_NUMBER] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

// 每个方向的传感器映射 (兼容4传感器模式)
static const uint8_t direction_sensors[ROBOT_DIRECTION_NUMBER][2] = {
    {ROBOT_SENSOR_F1, ROBOT_SENSOR_F2},  // 前方: 0,1
    {ROBOT_SENSOR_R1, ROBOT_SENSOR_R2},  // 右侧: 2,3
    {ROBOT_SENSOR_R1, ROBOT_SENSOR_R2},  // 后方: 2,3 (复用右侧传感器)
    {ROBOT_SENSOR_F1, ROBOT_SENSOR_F2}   // 左侧: 0,1 (复用前方传感器)
};

// 方向轮询发波模式 (兼容4传感器模式)
static const uint16_t direction_burst_patterns[ROBOT_DIRECTION_NUMBER] = {
    0x03,  // 前方: F1+F2 (bit0+bit1)
    0x0C,  // 右侧: R1+R2 (bit2+bit3)
    0x0C,  // 后方: R1+R2 (bit2+bit3) - 复用右侧
    0x03   // 左侧: F1+F2 (bit0+bit1) - 复用前方
};

// 单传感器发波模式
static const uint16_t single_burst_patterns[ROBOT_SENSOR_NUMBER] = {
    0x01, 0x02, 0x04, 0x08,  // F1,F2,R1,R2
    0x10, 0x20, 0x40, 0x80   // B1,B2,L1,L2
};

/**
 * @brief 调试：简单距离测试，绕过复杂滤波
 */
void Robot_SimpleDistanceTest(void)
{
#if DEBUG_SIMPLE_DISTANCE_TEST
    DEBUG_SIMPLE_TEST("=== Simple Distance Test ===\r\n");

    // 直接读取传感器数据并计算距离
    for(uint8_t modbus_id = 1; modbus_id <= 4; modbus_id++) {
        SensorEchoInfoTypedef sensor_data;
        if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
            uint16_t echo_time = sensor_data.EchoInfo[0].u16EchoTime;
            uint16_t simple_distance = (echo_time > 0 && echo_time < 20000) ? echo_time / 58 : 0;

            DEBUG_SIMPLE_TEST("Sensor%d: EchoTime=%d, SimpleDistance=%dcm\r\n",
                              modbus_id-1, echo_time, simple_distance);
        } else {
            DEBUG_SIMPLE_TEST("Sensor%d: Read FAILED\r\n", modbus_id-1);
        }
    }
    DEBUG_SIMPLE_TEST("============================\r\n");
#endif
}

/**
 * @brief 调试：打印原始传感器数据
 */
void Robot_DebugPrintSensorData(void)
{
    DEBUG_ROBOT("=== Raw Sensor Data Debug ===\r\n");
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        DEBUG_ROBOT("Gu8SysDistance[%d] = %d (0x%02X)\r\n",
                    i, Gu8SysDistance[i], Gu8SysDistance[i]);
    }
    DEBUG_ROBOT("NO_OBJECT = %d (0x%02X)\r\n", NO_OBJECT, NO_OBJECT);
    DEBUG_ROBOT("=============================\r\n");

    // 关键系统信息 - 始终输出
    DEBUG_CRITICAL("SensorData: [%d,%d,%d,%d] Status: [%d,%d,%d,%d]\r\n",
                   Gu8SysDistance[0], Gu8SysDistance[1], Gu8SysDistance[2], Gu8SysDistance[3],
                   Gu8SensorErrSta[0], Gu8SensorErrSta[1], Gu8SensorErrSta[2], Gu8SensorErrSta[3]);
}

/**
 * @brief 机器人超声波系统初始化
 */
void Robot_UltrasonicInit(void)
{
    // 初始化配置参数
    GstrRobotConfig.u16MinSafeDistance = ROBOT_CRITICAL_DISTANCE;
    GstrRobotConfig.u16MaxDetectDistance = 5000; // 5m
    GstrRobotConfig.u8UpdateFrequency = ROBOT_FREQ_NORMAL;
    GstrRobotConfig.eMotionState = ROBOT_MOTION_STOP;
    GstrRobotConfig.u8EnableAdaptiveMode = 1;
    
    // 初始化方向信息
    memset(&GstrRobotDirectionInfo, 0, sizeof(RobotDirectionInfoTypedef));
    for(uint8_t i = 0; i < ROBOT_DIRECTION_NUMBER; i++) {
        GstrRobotDirectionInfo.u16Distance[i] = 0xFFFF; // 无障碍物
        GstrRobotDirectionInfo.u8Confidence[i] = 0;
        GstrRobotDirectionInfo.u8WarningLevel[i] = ROBOT_WARNING_SAFE;
    }
    
    // 初始化传感器信息
    memset(GstrRobotSensorInfo, 0, sizeof(GstrRobotSensorInfo));
    
    printf("Robot Ultrasonic System Initialized\r\n");
}

/**
 * @brief 机器人超声波主任务
 */
void Robot_UltrasonicTask(void)
{
    static uint8_t direction_index = 0;
    static uint32_t cycle_count = 0;
    
    Robot_UltrasonicInit();
    
    for(;;) {
        uint32_t start_time = osKernelSysTick();
        
        // 1. 获取当前运动状态
        tenu_RobotMotionStateTypedef motion_state = Robot_GetMotionState();
        
        // 2. 根据运动状态选择发波策略
        uint16_t burst_pattern = Robot_SelectBurstPattern(motion_state, direction_index);
        
        // 3. 执行测距
        Robot_ProcessDirection(direction_index, burst_pattern);
        
        // 4. 更新方向索引
        direction_index = (direction_index + 1) % ROBOT_DIRECTION_NUMBER;
        
        // 5. 完成一轮后进行全局处理
        if(direction_index == 0) {
            cycle_count++;
            Robot_GlobalProcessing();
            
            if(cycle_count % 10 == 0) {
                printf("Robot Cycle %lu completed\r\n", cycle_count);
            }
        }
        
        // 6. 动态延时控制
        uint16_t delay_ms = Robot_GetAdaptiveDelay(motion_state);
        uint32_t elapsed = osKernelSysTick() - start_time;
        if(delay_ms > elapsed) {
            osDelay(delay_ms - elapsed);
        }
    }
}

/**
 * @brief 处理指定方向的传感器数据
 */
void Robot_ProcessDirection(uint8_t direction, uint16_t burst_pattern)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;
    
    // 1. 发波控制
    if(US_SetBurstMode(&ModbusH2, burst_pattern) != MODBUS_Q_OK) {
        printf("Failed to set burst mode for direction %d\r\n", direction);
        return;
    }
    
    // 2. 等待测距完成
    osDelay(50);
    
    // 3. 读取该方向所有传感器数据
    for(uint8_t i = 0; i < 2; i++) {
        uint8_t sensor_id = direction_sensors[direction][i];
        uint8_t modbus_id = sensor_id + 1; // Modbus ID从1开始
        
        if(burst_pattern & (0x01 << sensor_id)) {
            SensorEchoInfoTypedef sensor_data;
            if(US_ReadEchoInfo(&ModbusH2, modbus_id, &sensor_data) == MODBUS_Q_OK) {
                // Modbus通信调试
                DEBUG_MODBUS("Sensor%d Modbus read OK - EchoTime[0]=%d, RingingTime=%d, SampleFreq=%d\r\n",
                             sensor_id, sensor_data.EchoInfo[0].u16EchoTime,
                             sensor_data.u16RingingTime, sensor_data.u8SampleFreq);

                // 4. 处理单个传感器数据
                UltrasonicAlg_ProcessData(&sensor_data, sensor_id);
                
                // 5. 更新机器人传感器信息
                GstrRobotSensorInfo[sensor_id].u16RingingTime = sensor_data.u16RingingTime;
                GstrRobotSensorInfo[sensor_id].u8SampleFreq = sensor_data.u8SampleFreq;
                GstrRobotSensorInfo[sensor_id].u8SnsStaFlg = sensor_data.u8SnsStaFlg;
                
                // 6. 更新距离信息 - 直接使用Gu8SysDistance
                // 不需要复制，直接在融合时使用Gu8SysDistance
                GstrRobotDirectionInfo.u8SensorStatus[sensor_id] = 1; // 标记为正常
            } else {
                printf("Failed to read sensor %d data\r\n", sensor_id);
                GstrRobotDirectionInfo.u8SensorStatus[sensor_id] = 0; // 标记为故障
            }
        }
    }
    
    // 7. 方向级数据融合
    Robot_DirectionDataFusion(direction);
    
    // 8. 更新时间戳
    GstrRobotDirectionInfo.u32LastUpdateTime[direction] = osKernelSysTick();
}

/**
 * @brief 方向级数据融合
 */
void Robot_DirectionDataFusion(uint8_t direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;
    
    uint8_t sensor1 = direction_sensors[direction][0];
    uint8_t sensor2 = direction_sensors[direction][1];
    
    // 距离数据获取和转换
    uint16_t dist1 = 0xFFFF, dist2 = 0xFFFF;

    if(sensor1 < SENSOR_NUMBER) {
        if(Gu8SysDistance[sensor1] != NO_OBJECT) {
            dist1 = Gu8SysDistance[sensor1] * 58;
        }
        DEBUG_ROBOT("Sensor%d raw=%d, converted=%dmm\r\n",
                    sensor1, Gu8SysDistance[sensor1], dist1);
    }

    if(sensor2 < SENSOR_NUMBER) {
        if(Gu8SysDistance[sensor2] != NO_OBJECT) {
            dist2 = Gu8SysDistance[sensor2] * 58;
        }
        DEBUG_ROBOT("Sensor%d raw=%d, converted=%dmm\r\n",
                    sensor2, Gu8SysDistance[sensor2], dist2);
    }
    
    // 1. 数据有效性检查 (适应实际环境)
    uint8_t valid1 = (dist1 != 0xFFFF && dist1 > 100 && dist1 < 20000); // 10cm-20m
    uint8_t valid2 = (dist2 != 0xFFFF && dist2 > 100 && dist2 < 20000); // 10cm-20m

    DEBUG_ROBOT("Direction %d - valid1=%d, valid2=%d\r\n", direction, valid1, valid2);
    
    uint16_t fused_distance = 0xFFFF;
    
    if(valid1 && valid2) {
        // 2. 双传感器数据融合
        uint16_t diff = (dist1 > dist2) ? (dist1 - dist2) : (dist2 - dist1);
        if(diff < 100) { // 差异小于10cm，取平均值
            fused_distance = (dist1 + dist2) / 2;
        } else {
            // 差异较大，取较小值（更保守）
            fused_distance = (dist1 < dist2) ? dist1 : dist2;
        }
    } else if(valid1) {
        fused_distance = dist1;
    } else if(valid2) {
        fused_distance = dist2;
    }
    
    // 3. 更新方向距离
    GstrRobotDirectionInfo.u16Distance[direction] = fused_distance;
    GstrRobotDirectionInfo.u8Confidence[direction] = Robot_CalculateConfidence(valid1, valid2, dist1, dist2);
    
    // 4. 调试输出
    printf("Direction %d: Sensor%d=%dmm, Sensor%d=%dmm, Fused=%dmm, Confidence=%d%%\r\n",
           direction, sensor1, dist1, sensor2, dist2, fused_distance, 
           GstrRobotDirectionInfo.u8Confidence[direction]);
}

/**
 * @brief 选择发波策略
 */
uint16_t Robot_SelectBurstPattern(tenu_RobotMotionStateTypedef motion_state, uint8_t direction_index)
{
    if(!GstrRobotConfig.u8EnableAdaptiveMode) {
        // 非自适应模式，正常轮询
        return direction_burst_patterns[direction_index];
    }
    
    switch(motion_state) {
        case ROBOT_MOTION_FORWARD:
            // 前进时优先前方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_FORWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_BACKWARD:
            // 后退时优先后方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_BACKWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_BACKWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_TURN_LEFT:
            // 左转时优先左侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        case ROBOT_MOTION_TURN_RIGHT:
            // 右转时优先右侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        default: // ROBOT_MOTION_STOP
            // 停止时正常轮询
            return direction_burst_patterns[direction_index];
    }
}

/**
 * @brief 获取自适应延时
 */
uint16_t Robot_GetAdaptiveDelay(tenu_RobotMotionStateTypedef motion_state)
{
    switch(motion_state) {
        case ROBOT_MOTION_STOP:
            return 1000 / ROBOT_FREQ_STOP;      // 200ms
        case ROBOT_MOTION_FORWARD:
        case ROBOT_MOTION_BACKWARD:
            return 1000 / ROBOT_FREQ_NORMAL;    // 50ms
        case ROBOT_MOTION_TURN_LEFT:
        case ROBOT_MOTION_TURN_RIGHT:
            return 1000 / ROBOT_FREQ_FAST;      // 20ms
        default:
            return 1000 / ROBOT_FREQ_SLOW;      // 100ms
    }
}

/**
 * @brief 更新警告系统
 */
void Robot_UpdateWarningSystem(void)
{
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint16_t distance = GstrRobotDirectionInfo.u16Distance[dir];

        if(distance == 0xFFFF || distance > ROBOT_SAFE_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_SAFE;
        } else if(distance > ROBOT_CAUTION_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CAUTION;
        } else if(distance > ROBOT_DANGER_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_DANGER;
        } else {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CRITICAL;
        }
    }
}

/**
 * @brief 全局数据处理
 */
void Robot_GlobalProcessing(void)
{
    // 0. 传感器1和3专项诊断
    printf("SENSOR_DIAG: [%d,%d,%d,%d] ErrSta: [%d,%d,%d,%d]\r\n",
           Gu8SysDistance[0], Gu8SysDistance[1], Gu8SysDistance[2], Gu8SysDistance[3],
           Gu8SensorErrSta[0], Gu8SensorErrSta[1], Gu8SensorErrSta[2], Gu8SensorErrSta[3]);

    // 1. 调试：打印原始传感器数据
    Robot_DebugPrintSensorData();

    // 1. 更新警告系统
    Robot_UpdateWarningSystem();

    // 2. 检查传感器状态
    uint8_t fault_count = 0;
    for(uint8_t i = 0; i < ROBOT_SENSOR_NUMBER; i++) {
        if(GstrRobotDirectionInfo.u8SensorStatus[i] == 0) {
            fault_count++;
        }
    }

    // 3. 输出状态报告
    printf("=== Robot Status Report ===\r\n");
    printf("Motion State: %d\r\n", GstrRobotConfig.eMotionState);
    printf("Sensor Faults: %d/%d\r\n", fault_count, ROBOT_SENSOR_NUMBER);

    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        const char* dir_names[] = {"Forward", "Right", "Backward", "Left"};
        printf("%s: %dmm (Warning:%d, Confidence:%d%%)\r\n",
               dir_names[dir],
               GstrRobotDirectionInfo.u16Distance[dir],
               GstrRobotDirectionInfo.u8WarningLevel[dir],
               GstrRobotDirectionInfo.u8Confidence[dir]);
    }
    printf("===========================\r\n");
}

/**
 * @brief 获取机器人运动状态
 */
tenu_RobotMotionStateTypedef Robot_GetMotionState(void)
{
    return GstrRobotConfig.eMotionState;
}

/**
 * @brief 设置机器人运动状态
 */
void Robot_SetMotionState(tenu_RobotMotionStateTypedef motion_state)
{
    GstrRobotConfig.eMotionState = motion_state;
    printf("Robot motion state changed to: %d\r\n", motion_state);
}

/**
 * @brief 获取指定方向的距离
 */
uint16_t Robot_GetDirectionDistance(tenu_RobotDirectionTypedef direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return 0xFFFF;
    return GstrRobotDirectionInfo.u16Distance[direction];
}

/**
 * @brief 获取指定方向的警告等级
 */
tenu_RobotWarningLevelTypedef Robot_GetDirectionWarningLevel(tenu_RobotDirectionTypedef direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return ROBOT_WARNING_SAFE;
    return (tenu_RobotWarningLevelTypedef)GstrRobotDirectionInfo.u8WarningLevel[direction];
}

/**
 * @brief 检查是否可以安全移动
 */
uint8_t Robot_IsSafeToMove(tenu_RobotDirectionTypedef direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return 0;

    uint16_t distance = GstrRobotDirectionInfo.u16Distance[direction];
    uint8_t confidence = GstrRobotDirectionInfo.u8Confidence[direction];

    // 距离足够且数据可信度高
    if(distance > GstrRobotConfig.u16MinSafeDistance && confidence > 50) {
        return 1;
    }

    return 0;
}

/**
 * @brief 计算数据可信度
 */
uint8_t Robot_CalculateConfidence(uint8_t valid1, uint8_t valid2, uint16_t dist1, uint16_t dist2)
{
    if(!valid1 && !valid2) return 0;
    if(valid1 && !valid2) return 60;
    if(!valid1 && valid2) return 60;

    // 两个传感器都有效
    uint16_t diff = (dist1 > dist2) ? (dist1 - dist2) : (dist2 - dist1);
    if(diff < 50) return 95;      // 差异<5cm，高可信度
    if(diff < 100) return 80;     // 差异<10cm，较高可信度
    if(diff < 200) return 65;     // 差异<20cm，中等可信度
    return 50;                    // 差异较大，低可信度
}
