/**
 * @file robot_ultrasonic.c
 * @brief 机器人底盘8传感器超声波测距系统实现
 * @date 2024-07-08
 */

#include "robot_ultrasonic.h"
#include "cmsis_os.h"
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include "modbus_interface.h"
#include "echo_processing.h"
#include "debug_config.h"

// 外部变量声明
extern modbusHandler_t ModbusH2;

// 距离阈值定义
#define ROBOT_SAFE_DISTANCE      2000  // 2m - 安全距离
#define ROBOT_CAUTION_DISTANCE   1000  // 1m - 注意距离  
#define ROBOT_DANGER_DISTANCE    500   // 0.5m - 危险距离
#define ROBOT_CRITICAL_DISTANCE  200   // 0.2m - 紧急距离

// 更新频率定义
#define ROBOT_FREQ_STOP          5     // 停止时5Hz
#define ROBOT_FREQ_SLOW          10    // 慢速时10Hz
#define ROBOT_FREQ_NORMAL        20    // 正常时20Hz
#define ROBOT_FREQ_FAST          50    // 快速时50Hz

// 全局变量定义
RobotDirectionInfoTypedef GstrRobotDirectionInfo;
RobotConfigTypedef GstrRobotConfig;
SingleSensorObjectInfo GstrRobotSensorInfo[ROBOT_SENSOR_NUMBER];
uint8_t Gu8RobotSysDistance[ROBOT_SENSOR_NUMBER] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

// E52436传感器三角定位配置：支持同面和跨面测量
typedef struct {
    uint8_t master_sensor;        // 主发波传感器
    uint8_t same_face_sensor;     // 同面接收传感器
    uint8_t cross_face_left;      // 跨面左侧接收传感器
    uint8_t cross_face_right;     // 跨面右侧接收传感器
    uint8_t direction;            // 对应的机器人方向
    float same_face_spacing;      // 同面传感器间距(cm)
    float cross_face_spacing;     // 跨面传感器间距(cm)
    uint8_t face_id;              // 面ID (0=前, 1=右, 2=后, 3=左)
    uint8_t priority;             // 优先级(1=最高)
} E52436_TriangleConfig_t;

// 传感器物理布局参数 (可配置)
#define SENSOR_HEIGHT_CM        30.0f    // 传感器离地高度
#define SENSOR_SAME_FACE_SPACING_CM  30.0f    // 同面传感器间距
#define SENSOR_CROSS_FACE_SPACING_CM 30.0f    // 跨面传感器间距
#define SENSOR_DIAGONAL_SPACING_CM   42.43f   // 对角传感器间距 (30*√2)
#define TRIANGLE_MIN_DISTANCE   5.0f     // 三角测量最小距离
#define TRIANGLE_MAX_DISTANCE   200.0f   // 三角测量最大距离

// 测量置信度参数 (可配置)
#define SAME_FACE_CONFIDENCE_BASE    90   // 同面测量基础置信度
#define CROSS_FACE_CONFIDENCE_BASE   70   // 跨面测量基础置信度
#define DIAGONAL_CONFIDENCE_BASE     50   // 对角测量基础置信度
#define CONSISTENCY_BONUS           10    // 一致性奖励
#define INCONSISTENCY_PENALTY       15    // 不一致性惩罚

// 方形底盘传感器布局 (0-7对应1-8号传感器)
// 根据用户图片：每面2个传感器，间距30cm，离地30cm
//    7 ---- 0 ---- 1
//    |             |
//    6             2
//    |             |
//    5 ---- 4 ---- 3

// E52436三角定位配置表 (8传感器方形底盘，1-8轮询发波)
static const E52436_TriangleConfig_t e52436_configs[] = {
    // 传感器0发波 (前面左侧)
    {0, 1, 7, 2, ROBOT_DIRECTION_FORWARD, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 0, 1},

    // 传感器1发波 (前面右侧)
    {1, 0, 2, 7, ROBOT_DIRECTION_FORWARD, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 0, 1},

    // 传感器2发波 (右面上侧)
    {2, 3, 1, 4, ROBOT_DIRECTION_RIGHT, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 1, 1},

    // 传感器3发波 (右面下侧)
    {3, 2, 4, 1, ROBOT_DIRECTION_RIGHT, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 1, 1},

    // 传感器4发波 (后面右侧)
    {4, 5, 3, 6, ROBOT_DIRECTION_BACKWARD, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 2, 1},

    // 传感器5发波 (后面左侧)
    {5, 4, 6, 3, ROBOT_DIRECTION_BACKWARD, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 2, 1},

    // 传感器6发波 (左面下侧)
    {6, 7, 5, 0, ROBOT_DIRECTION_LEFT, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 3, 1},

    // 传感器7发波 (左面上侧)
    {7, 6, 0, 5, ROBOT_DIRECTION_LEFT, SENSOR_SAME_FACE_SPACING_CM, SENSOR_CROSS_FACE_SPACING_CM, 3, 1},
};

#define E52436_CONFIG_COUNT (sizeof(e52436_configs)/sizeof(e52436_configs[0]))

// 三角测量结果结构
typedef struct {
    float distance;           // 计算出的距离
    float angle;             // 计算出的角度
    uint8_t confidence;      // 置信度(0-100)
    uint8_t valid;           // 结果有效性
    uint16_t master_echo;    // 主发回波时间
    uint16_t left_echo;      // 左侧回波时间
    uint16_t right_echo;     // 右侧回波时间
} TriangleResult_t;

// 方向轮询发波模式 (兼容4传感器模式)
static const uint16_t direction_burst_patterns[ROBOT_DIRECTION_NUMBER] = {
    0x03,  // 前方: F1+F2 (bit0+bit1)
    0x0C,  // 右侧: R1+R2 (bit2+bit3)
    0x0C,  // 后方: R1+R2 (bit2+bit3) - 复用右侧
    0x03   // 左侧: F1+F2 (bit0+bit1) - 复用前方
};

// 单传感器发波模式
static const uint16_t single_burst_patterns[ROBOT_SENSOR_NUMBER] = {
    0x01, 0x02, 0x04, 0x08,  // F1,F2,R1,R2
    0x10, 0x20, 0x40, 0x80   // B1,B2,L1,L2
};


// 3. 智能回波可靠性评估和选择 (同面优先，跨面辅助)
    typedef struct {
        uint16_t echo_time;
        float distance;
        uint8_t reliability_score;
        uint8_t measurement_type; // 0=主发, 1=同面, 2=跨面左, 3=跨面右
        const char* name;
        float spacing;           // 与主发传感器的间距
    } EchoCandidate_t;

/**
 * @brief E52436三角测量算法 (支持同面和跨面测量)
 * @param config 传感器配置
 * @param master_echo 主发传感器回波时间
 * @param same_face_echo 同面传感器回波时间
 * @param cross_left_echo 跨面左侧传感器回波时间
 * @param cross_right_echo 跨面右侧传感器回波时间
 * @return 三角测量结果
 */
TriangleResult_t E52436_TriangleMeasurement(const E52436_TriangleConfig_t* config,
                                           uint16_t master_echo,
                                           uint16_t same_face_echo,
                                           uint16_t cross_left_echo,
                                           uint16_t cross_right_echo)
{
    TriangleResult_t result = {0};

    // 1. 回波时间有效性检查
    uint8_t master_valid = (master_echo < 20000 && master_echo > 100);
    uint8_t same_face_valid = (same_face_echo < 20000 && same_face_echo > 100);
    uint8_t cross_left_valid = (cross_left_echo < 20000 && cross_left_echo > 100);
    uint8_t cross_right_valid = (cross_right_echo < 20000 && cross_right_echo > 100);

    if(!master_valid) {
        printf("Triangle[%d]: Master echo invalid (%d)\r\n", config->master_sensor, master_echo);
        return result;
    }

    // 2. 主发距离计算 (基础距离)
    float master_distance = master_echo / 58.0f;

    printf("Triangle[%d]: Face=%d, Master=%.1fcm, SameFace=%s, CrossL=%s, CrossR=%s\r\n",
           config->master_sensor, config->face_id, master_distance,
           same_face_valid ? "OK" : "INVALID",
           cross_left_valid ? "OK" : "INVALID",
           cross_right_valid ? "OK" : "INVALID");



    EchoCandidate_t candidates[4] = {0};
    uint8_t candidate_count = 0;

    // 主发回波 (总是可用)
    candidates[candidate_count].echo_time = master_echo;
    candidates[candidate_count].distance = master_distance;
    candidates[candidate_count].reliability_score = 70; // 基础分数
    candidates[candidate_count].measurement_type = 0;
    candidates[candidate_count].name = "MASTER";
    candidates[candidate_count].spacing = 0;
    candidate_count++;

    // 同面监听回波 (最高优先级)
    if(same_face_valid) 
		{
        float same_face_distance = same_face_echo / 58.0f;
        uint8_t score = SAME_FACE_CONFIDENCE_BASE; // 90分基础

        // 距离合理性检查
        if(same_face_distance >= 5 && same_face_distance <= 200) score += 10;

        // 与主发回波的一致性检查 (同面应该很一致)
        float distance_diff = fabs(same_face_distance - master_distance);
        if(distance_diff < 3) score += CONSISTENCY_BONUS;      // 差异<3cm，很好
        else if(distance_diff < 8) score += 5;                 // 差异<8cm，还行
        else score -= INCONSISTENCY_PENALTY;                   // 差异太大，惩罚

        candidates[candidate_count].echo_time = same_face_echo;
        candidates[candidate_count].distance = same_face_distance;
        candidates[candidate_count].reliability_score = score;
        candidates[candidate_count].measurement_type = 1;
        candidates[candidate_count].name = "SAME_FACE";
        candidates[candidate_count].spacing = config->same_face_spacing;
        candidate_count++;
    }

    // 跨面左侧监听回波
    if(cross_left_valid) 
		{
        float cross_left_distance = cross_left_echo / 58.0f;
        uint8_t score = CROSS_FACE_CONFIDENCE_BASE; // 70分基础

        // 距离合理性检查
        if(cross_left_distance >= 5 && cross_left_distance <= 200) score += 10;

        // 与主发回波的一致性检查 (跨面可能有差异)
        float distance_diff = fabs(cross_left_distance - master_distance);
        if(distance_diff < 10) score += 10;        // 差异<10cm，可接受
        else if(distance_diff < 20) score += 5;    // 差异<20cm，勉强

        candidates[candidate_count].echo_time = cross_left_echo;
        candidates[candidate_count].distance = cross_left_distance;
        candidates[candidate_count].reliability_score = score;
        candidates[candidate_count].measurement_type = 2;
        candidates[candidate_count].name = "CROSS_LEFT";
        candidates[candidate_count].spacing = config->cross_face_spacing;
        candidate_count++;
    }

    // 跨面右侧监听回波
    if(cross_right_valid) 
		{
        float cross_right_distance = cross_right_echo / 58.0f;
        uint8_t score = CROSS_FACE_CONFIDENCE_BASE; // 70分基础

        // 距离合理性检查
        if(cross_right_distance >= 5 && cross_right_distance <= 200) score += 10;

        // 与主发回波的一致性检查 (跨面可能有差异)
        float distance_diff = fabs(cross_right_distance - master_distance);
        if(distance_diff < 10) score += 10;        // 差异<10cm，可接受
        else if(distance_diff < 20) score += 5;    // 差异<20cm，勉强

        candidates[candidate_count].echo_time = cross_right_echo;
        candidates[candidate_count].distance = cross_right_distance;
        candidates[candidate_count].reliability_score = score;
        candidates[candidate_count].measurement_type = 3;
        candidates[candidate_count].name = "CROSS_RIGHT";
        candidates[candidate_count].spacing = config->cross_face_spacing;
        candidate_count++;
    }

    // 选择最可靠的监听回波
    EchoCandidate_t* best_listen = NULL;
    uint8_t best_score = 0;

    for(uint8_t i = 1; i < candidate_count; i++) { // 跳过主发回波(索引0)
        if(candidates[i].reliability_score > best_score) {
            best_score = candidates[i].reliability_score;
            best_listen = &candidates[i];
        }
    }

    printf("Triangle[%d]: Echo candidates - ", config->master_sensor);
    for(uint8_t i = 0; i < candidate_count; i++) {
        printf("%s:%.1fcm(score=%d) ", candidates[i].name, candidates[i].distance, candidates[i].reliability_score);
    }
    printf("\r\n");

    if(best_listen == NULL || best_score < 60) {
        // 没有可靠的监听回波，使用主发距离
        result.distance = master_distance;
        result.angle = 0;
        result.confidence = 50;
        result.valid = (master_distance >= TRIANGLE_MIN_DISTANCE && master_distance <= TRIANGLE_MAX_DISTANCE);
        printf("Triangle[%d]: No reliable listen echo, using master distance %.1fcm\r\n",
               config->master_sensor, master_distance);
        return result;
    }

    // 4. 执行三角测量计算
    float listen_distance = best_listen->distance;
    float distance_diff = listen_distance - master_distance;

    // 使用几何关系计算精确距离和角度
    float sensor_spacing = config->same_face_spacing;
    float angle_rad = atan2(distance_diff, sensor_spacing);
    float corrected_distance;

    // 根据监听传感器位置调整计算// 0=主发, 1=同面, 2=跨面左, 3=跨面右
    if(best_listen->measurement_type == 2) {
        // 左侧监听：负角度
        angle_rad = -fabs(angle_rad);
        corrected_distance = master_distance * cos(angle_rad);
    } else {
        // 右侧监听：正角度
        angle_rad = fabs(angle_rad);
        corrected_distance = master_distance * cos(angle_rad);
    }

    // 5. 多种测量方法融合
    float final_distance;
    uint8_t final_confidence;

    if(candidate_count >= 3 && cross_left_valid && cross_right_valid) {
        // 三点测量：主发+左右监听
        float left_distance = candidates[1].distance;
        float right_distance = candidates[2].distance;

        // 检查三点一致性
        float max_diff = fmax(fmax(fabs(master_distance - left_distance),
                                  fabs(master_distance - right_distance)),
                             fabs(left_distance - right_distance));

        if(max_diff < 10) {
            // 三点一致性好，取加权平均
            final_distance = (master_distance * 0.5f + left_distance * 0.25f + right_distance * 0.25f);
            final_confidence = 95;
            printf("Triangle[%d]: 3-point consistent (diff=%.1fcm), weighted avg=%.1fcm\r\n",
                   config->master_sensor, max_diff, final_distance);
        } else {
            // 三点不一致，使用最可靠的双点测量
            final_distance = corrected_distance;
            final_confidence = best_score;
            printf("Triangle[%d]: 3-point inconsistent (diff=%.1fcm), using best 2-point=%.1fcm\r\n",
                   config->master_sensor, max_diff, final_distance);
        }
    } else {
        // 双点测量：主发+最佳监听
        final_distance = corrected_distance;
        final_confidence = best_score;
        printf("Triangle[%d]: 2-point measurement, distance=%.1fcm\r\n",
               config->master_sensor, final_distance);
    }

    // 6. 结果验证和输出
    if(final_distance >= TRIANGLE_MIN_DISTANCE && final_distance <= TRIANGLE_MAX_DISTANCE) {
        result.distance = final_distance;
        result.angle = angle_rad * 180.0f / 3.14159f;
        result.confidence = final_confidence;
        result.valid = 1;

        printf("Triangle[%d]: FINAL - Master=%.1fcm, %s=%.1fcm, Result=%.1fcm, Angle=%.1f°, Conf=%d%%\r\n",
               config->master_sensor, master_distance, best_listen->name, listen_distance,
               final_distance, result.angle, result.confidence);
    } else {
        printf("Triangle[%d]: Result out of range (%.1fcm), using master distance\r\n",
               config->master_sensor, final_distance);
        result.distance = master_distance;
        result.angle = 0;
        result.confidence = 50;
        result.valid = 1;
    }

    // 记录原始数据
    result.master_echo = master_echo;
    result.left_echo = cross_left_echo;
    result.right_echo = cross_right_echo;

    return result;
}


/**
 * @brief 调试：打印原始传感器数据
 */
void Robot_DebugPrintSensorData(void)
{
    DEBUG_ROBOT("=== Raw Sensor Data Debug ===\r\n");
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        DEBUG_ROBOT("Gu8SysDistance[%d] = %d (0x%02X)\r\n",
                    i, Gu8SysDistance[i], Gu8SysDistance[i]);
    }
    DEBUG_ROBOT("NO_OBJECT = %d (0x%02X)\r\n", NO_OBJECT, NO_OBJECT);
    DEBUG_ROBOT("=============================\r\n");

    // 关键系统信息 - 始终输出
    DEBUG_CRITICAL("SensorData: [%d,%d,%d,%d] Status: [%d,%d,%d,%d]\r\n",
                   Gu8SysDistance[0], Gu8SysDistance[1], Gu8SysDistance[2], Gu8SysDistance[3],
                   Gu8SensorErrSta[0], Gu8SensorErrSta[1], Gu8SensorErrSta[2], Gu8SensorErrSta[3]);
}

/**
 * @brief 机器人超声波系统初始化
 */
void Robot_UltrasonicInit(void)
{
    // 初始化配置参数
    GstrRobotConfig.u16MinSafeDistance = ROBOT_CRITICAL_DISTANCE;
    GstrRobotConfig.u16MaxDetectDistance = 5000; // 5m
    GstrRobotConfig.u8UpdateFrequency = ROBOT_FREQ_NORMAL;
    GstrRobotConfig.eMotionState = ROBOT_MOTION_STOP;
    GstrRobotConfig.u8EnableAdaptiveMode = 1;
    
    // 初始化方向信息
    memset(&GstrRobotDirectionInfo, 0, sizeof(RobotDirectionInfoTypedef));
    for(uint8_t i = 0; i < ROBOT_DIRECTION_NUMBER; i++) {
        GstrRobotDirectionInfo.u16Distance[i] = 0xFFFF; // 无障碍物
        GstrRobotDirectionInfo.u8Confidence[i] = 0;
        GstrRobotDirectionInfo.u8WarningLevel[i] = ROBOT_WARNING_SAFE;
    }
    
    // 初始化传感器信息
    memset(GstrRobotSensorInfo, 0, sizeof(GstrRobotSensorInfo));
    
    printf("Robot Ultrasonic System Initialized\r\n");
}


/**
 * @brief E52436三角定位处理 (完整版)
 */
void Robot_ProcessE52436Triangle(uint8_t config_index)
{
    if(config_index >= E52436_CONFIG_COUNT) return;

    const E52436_TriangleConfig_t* config = &e52436_configs[config_index];
    uint8_t master_sensor = config->master_sensor;
    uint16_t burst_pattern = single_burst_patterns[master_sensor];

    printf("=== E52436 Triangle[%d]: Master=%d, SameFace=%d, CrossL=%d, CrossR=%d, Face=%d ===\r\n",
           config_index, master_sensor, config->same_face_sensor,
           config->cross_face_left, config->cross_face_right, config->face_id);

    // 1. 设置单传感器发波模式
    if(US_SetBurstMode(&ModbusH2, burst_pattern) != MODBUS_Q_OK) {
        printf("Failed to set burst mode for sensor %d\r\n", master_sensor);
        return;
    }

    // 2. 等待测距完成
    osDelay(50);

    // 3. 读取所有相关传感器的回波数据
    uint16_t master_echo = 0xFFFF, same_face_echo = 0xFFFF;
    uint16_t cross_left_echo = 0xFFFF, cross_right_echo = 0xFFFF;

    // 读取主发传感器
    uint8_t master_modbus_id = master_sensor + 1;
    SensorEchoInfoTypedef master_data;
    if(US_ReadEchoInfo(&ModbusH2, master_modbus_id, &master_data) == MODBUS_Q_OK) {
        master_echo = master_data.EchoInfo[0].u16EchoTime;
        printf("Master Sensor%d: EchoTime=%d\r\n", master_sensor, master_echo);

        // 处理主发传感器数据 (用于算法处理)
        UltrasonicAlg_ProcessData(&master_data, (tenu_SensorIDTypedef)master_sensor);
    } else {
        printf("Failed to read master sensor %d\r\n", master_sensor);
        return;
    }

    // 读取同面接收传感器
    uint8_t same_face_modbus_id = config->same_face_sensor + 1;
    SensorEchoInfoTypedef same_face_data;
    if(US_ReadEchoInfo(&ModbusH2, same_face_modbus_id, &same_face_data) == MODBUS_Q_OK) {
        same_face_echo = same_face_data.EchoInfo[0].u16EchoTime;
        printf("SameFace Sensor%d: EchoTime=%d\r\n", config->same_face_sensor, same_face_echo);
    }

    // 读取跨面左侧接收传感器
    uint8_t cross_left_modbus_id = config->cross_face_left + 1;
    SensorEchoInfoTypedef cross_left_data;
    if(US_ReadEchoInfo(&ModbusH2, cross_left_modbus_id, &cross_left_data) == MODBUS_Q_OK) {
        cross_left_echo = cross_left_data.EchoInfo[0].u16EchoTime;
        printf("CrossLeft Sensor%d: EchoTime=%d\r\n", config->cross_face_left, cross_left_echo);
    }

    // 读取跨面右侧接收传感器
    uint8_t cross_right_modbus_id = config->cross_face_right + 1;
    SensorEchoInfoTypedef cross_right_data;
    if(US_ReadEchoInfo(&ModbusH2, cross_right_modbus_id, &cross_right_data) == MODBUS_Q_OK) {
        cross_right_echo = cross_right_data.EchoInfo[0].u16EchoTime;
        printf("CrossRight Sensor%d: EchoTime=%d\r\n", config->cross_face_right, cross_right_echo);
    }

    // 4. 执行三角测量
    TriangleResult_t triangle_result = E52436_TriangleMeasurement(config, master_echo,
                                                                 same_face_echo, cross_left_echo, cross_right_echo);

    // 5. 更新传感器距离数据 (如果三角测量有效且置信度高)
    if(triangle_result.valid && triangle_result.confidence >= 60) {
        uint8_t new_distance = (uint8_t)(triangle_result.distance + 0.5f); // 四舍五入
        if(new_distance >= 5 && new_distance <= 200) {
            printf("Triangle UPDATE: Sensor%d %dcm -> %dcm (Conf=%d%%, Angle=%.1f°)\r\n",
                   master_sensor, Gu8SysDistance[master_sensor], new_distance,
                   triangle_result.confidence, triangle_result.angle);
            Gu8SysDistance[master_sensor] = new_distance;
        }
    }

    printf("Final: Sensor%d Distance=%dcm, Confidence=%d%%\r\n",
           master_sensor, Gu8SysDistance[master_sensor], triangle_result.confidence);
}

/**
 * @brief 计算每个面的障碍物距离
 * @param face_id 面ID (0=前, 1=右, 2=后, 3=左)
 * @return 该面的最终距离(mm)
 */
uint16_t Robot_CalculateFaceDistance(uint8_t face_id)
{
    if(face_id >= 4) return 0xFFFF;

    // 收集该面的所有传感器数据
    uint16_t face_distances[2] = {0xFFFF, 0xFFFF};
    uint8_t face_confidences[2] = {0, 0};
    uint8_t valid_count = 0;

    // 根据面ID找到对应的传感器
    for(uint8_t i = 0; i < E52436_CONFIG_COUNT; i++) {
        if(e52436_configs[i].face_id == face_id) {
            uint8_t sensor_id = e52436_configs[i].master_sensor;
            uint8_t distance_cm = Gu8SysDistance[sensor_id];

            if(distance_cm != NO_OBJECT && distance_cm >= 5 && distance_cm <= 200) {
                face_distances[valid_count] = distance_cm * 58; // 转换为mm
                face_confidences[valid_count] = 80; // 基础置信度
                valid_count++;

                printf("Face%d Sensor%d: %dcm\r\n", face_id, sensor_id, distance_cm);

                if(valid_count >= 2) break; // 每面最多2个传感器
            }
        }
    }

    if(valid_count == 0) {
        printf("Face%d: No valid sensors\r\n", face_id);
        return 0xFFFF;
    }

    uint16_t final_distance;
    uint8_t final_confidence;

    if(valid_count == 1) {
        // 单传感器
        final_distance = face_distances[0];
        final_confidence = face_confidences[0];
    } else {
        // 双传感器融合
        uint16_t diff = (face_distances[0] > face_distances[1]) ?
                       (face_distances[0] - face_distances[1]) :
                       (face_distances[1] - face_distances[0]);

        if(diff < 580) { // 差异小于10cm，取平均值
            final_distance = (face_distances[0] + face_distances[1]) / 2;
            final_confidence = (face_confidences[0] + face_confidences[1]) / 2 + CONSISTENCY_BONUS;
        } else {
            // 差异较大，取较小值（保守策略）
            final_distance = (face_distances[0] < face_distances[1]) ? face_distances[0] : face_distances[1];
            final_confidence = (face_confidences[0] + face_confidences[1]) / 2 - INCONSISTENCY_PENALTY;
        }
    }

    printf("Face%d Final: Distance=%dmm, Confidence=%d%%, Sensors=%d\r\n",
           face_id, final_distance, final_confidence, valid_count);

    return final_distance;
}


/**
 * @brief 机器人超声波主任务
 */
void Robot_UltrasonicTask(void)
{
    static uint8_t sensor_index = 0;
    static uint32_t cycle_count = 0;

    Robot_UltrasonicInit();

    printf("=== E52436 Ultrasonic System Started ===\r\n");
    printf("Sensor sequence: 0->1->2->3 (single burst mode)\r\n");

    for(;;) {
        uint32_t start_time = osKernelSysTick();

        // 1. 获取当前运动状态
        tenu_RobotMotionStateTypedef motion_state = Robot_GetMotionState();

        // 2. E52436单传感器轮询模式
        printf("\r\n=== Sensor %d Burst Cycle %lu ===\r\n", sensor_index, cycle_count);
        Robot_ProcessE52436Triangle(sensor_index);

        // 3. 更新传感器索引
        sensor_index = (sensor_index + 1) % E52436_CONFIG_COUNT;

        // 4. 完成一轮传感器扫描后进行全局处理
        if(sensor_index == 0) {
            cycle_count++;

            // 更新所有方向的融合数据
            for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
                Robot_DirectionDataFusion(dir);
            }

            Robot_GlobalProcessing();

            if(cycle_count % 4 == 0) {
                printf("E52436 Cycle %lu completed (4 sensors scanned)\r\n", cycle_count);
            }
        }

        // 5. 动态延时控制
        uint16_t delay_ms = Robot_GetAdaptiveDelay(motion_state);
        uint32_t elapsed = osKernelSysTick() - start_time;
        if(delay_ms > elapsed) {
            osDelay(delay_ms - elapsed);
        }
    }
}


/**
 * @brief 处理指定方向的传感器数据 (兼容接口)
 */
void Robot_ProcessDirection(uint8_t direction, uint16_t burst_pattern)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;

    printf("=== Processing Direction %d ===\r\n", direction);

    // 根据方向找到对应的E52436配置并处理
    for(uint8_t i = 0; i < E52436_CONFIG_COUNT; i++) {
        if(e52436_configs[i].direction == direction) {
            Robot_ProcessE52436Triangle(i);
        }
    }

    // 方向级数据融合
    Robot_DirectionDataFusion(direction);

    // 更新时间戳
    GstrRobotDirectionInfo.u32LastUpdateTime[direction] = osKernelSysTick();
}

/**
 * @brief 方向级数据融合 (基于面级距离计算)
 */
void Robot_DirectionDataFusion(uint8_t direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;

    printf("=== Direction %d Data Fusion ===\r\n", direction);

    // 直接使用面级距离计算
    uint8_t face_id = direction; // 方向ID对应面ID
    uint16_t face_distance = Robot_CalculateFaceDistance(face_id);

    // 更新方向距离和置信度
    GstrRobotDirectionInfo.u16Distance[direction] = face_distance;

    // 根据距离有效性设置置信度
    if(face_distance != 0xFFFF && face_distance >= 290 && face_distance <= 11600) { // 5cm-200cm
        GstrRobotDirectionInfo.u8Confidence[direction] = 85; // 高置信度
    } else if(face_distance != 0xFFFF) {
        GstrRobotDirectionInfo.u8Confidence[direction] = 60; // 中等置信度
    } else {
        GstrRobotDirectionInfo.u8Confidence[direction] = 0;  // 无效
    }

    printf("Direction %d Final: Distance=%dmm, Confidence=%d%%\r\n",
           direction, face_distance, GstrRobotDirectionInfo.u8Confidence[direction]);




}


/**
 * @brief 选择发波策略
 */
uint16_t Robot_SelectBurstPattern(tenu_RobotMotionStateTypedef motion_state, uint8_t direction_index)
{
    if(!GstrRobotConfig.u8EnableAdaptiveMode) {
        // 非自适应模式，正常轮询
        return direction_burst_patterns[direction_index];
    }
    
    switch(motion_state) {
        case ROBOT_MOTION_FORWARD:
            // 前进时优先前方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_FORWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_BACKWARD:
            // 后退时优先后方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_BACKWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_BACKWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_TURN_LEFT:
            // 左转时优先左侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        case ROBOT_MOTION_TURN_RIGHT:
            // 右转时优先右侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        default: // ROBOT_MOTION_STOP
            // 停止时正常轮询
            return direction_burst_patterns[direction_index];
    }
}

/**
 * @brief 获取自适应延时
 */
uint16_t Robot_GetAdaptiveDelay(tenu_RobotMotionStateTypedef motion_state)
{
    switch(motion_state) {
        case ROBOT_MOTION_STOP:
            return 1000 / ROBOT_FREQ_STOP;      // 200ms
        case ROBOT_MOTION_FORWARD:
        case ROBOT_MOTION_BACKWARD:
            return 1000 / ROBOT_FREQ_NORMAL;    // 50ms
        case ROBOT_MOTION_TURN_LEFT:
        case ROBOT_MOTION_TURN_RIGHT:
            return 1000 / ROBOT_FREQ_FAST;      // 20ms
        default:
            return 1000 / ROBOT_FREQ_SLOW;      // 100ms
    }
}

/**
 * @brief 更新警告系统
 */
void Robot_UpdateWarningSystem(void)
{
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint16_t distance = GstrRobotDirectionInfo.u16Distance[dir];

        if(distance == 0xFFFF || distance > ROBOT_SAFE_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_SAFE;
        } else if(distance > ROBOT_CAUTION_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CAUTION;
        } else if(distance > ROBOT_DANGER_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_DANGER;
        } else {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CRITICAL;
        }
    }
}

/**
 * @brief 全局数据处理
 */
void Robot_GlobalProcessing(void)
{

    // 1. 调试：打印原始传感器数据
    Robot_DebugPrintSensorData();

    // 1. 更新警告系统
    Robot_UpdateWarningSystem();

    // 2. 检查传感器状态
    uint8_t fault_count = 0;
    for(uint8_t i = 0; i < ROBOT_SENSOR_NUMBER; i++) {
        if(GstrRobotDirectionInfo.u8SensorStatus[i] == 0) {
            fault_count++;
        }
    }

    // 3. 输出状态报告
    printf("=== Robot Status Report ===\r\n");
    printf("Motion State: %d\r\n", GstrRobotConfig.eMotionState);
    printf("Sensor Faults: %d/%d\r\n", fault_count, ROBOT_SENSOR_NUMBER);

    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        const char* dir_names[] = {"Forward", "Right", "Backward", "Left"};
        printf("%s: %dmm (Warning:%d, Confidence:%d%%)\r\n",
               dir_names[dir],
               GstrRobotDirectionInfo.u16Distance[dir],
               GstrRobotDirectionInfo.u8WarningLevel[dir],
               GstrRobotDirectionInfo.u8Confidence[dir]);
    }
    printf("===========================\r\n");
}

/**
 * @brief 获取机器人运动状态
 */
tenu_RobotMotionStateTypedef Robot_GetMotionState(void)
{
    return GstrRobotConfig.eMotionState;
}

/**
 * @brief 设置机器人运动状态
 */
void Robot_SetMotionState(tenu_RobotMotionStateTypedef motion_state)
{
    GstrRobotConfig.eMotionState = motion_state;
    printf("Robot motion state changed to: %d\r\n", motion_state);
}

/**
 * @brief 获取指定方向的距离
 */
uint16_t Robot_GetDirectionDistance(tenu_RobotDirectionTypedef direction)
{
    return GstrRobotDirectionInfo.u16Distance[direction];
}

/**
 * @brief 获取指定方向的警告等级
 */
tenu_RobotWarningLevelTypedef Robot_GetDirectionWarningLevel(tenu_RobotDirectionTypedef direction)
{
    return (tenu_RobotWarningLevelTypedef)GstrRobotDirectionInfo.u8WarningLevel[direction];
}

/**
 * @brief 检查是否可以安全移动
 */
uint8_t Robot_IsSafeToMove(tenu_RobotDirectionTypedef direction)
{

    uint16_t distance = GstrRobotDirectionInfo.u16Distance[direction];
    uint8_t confidence = GstrRobotDirectionInfo.u8Confidence[direction];

    // 距离足够且数据可信度高
    if(distance > GstrRobotConfig.u16MinSafeDistance && confidence > 50) {
        return 1;
    }

    return 0;
}

/**
 * @brief 计算数据可信度
 */
uint8_t Robot_CalculateConfidence(uint8_t valid1, uint8_t valid2, uint16_t dist1, uint16_t dist2)
{
    if(!valid1 && !valid2) return 0;
    if(valid1 && !valid2) return 60;
    if(!valid1 && valid2) return 60;

    // 两个传感器都有效
    uint16_t diff = (dist1 > dist2) ? (dist1 - dist2) : (dist2 - dist1);
    if(diff < 50) return 95;      // 差异<5cm，高可信度
    if(diff < 100) return 80;     // 差异<10cm，较高可信度
    if(diff < 200) return 65;     // 差异<20cm，中等可信度
    return 50;                    // 差异较大，低可信度
}
