/**
 * @file robot_ultrasonic.c
 * @brief 机器人底盘8传感器超声波测距系统实现
 * @date 2024-07-08
 */

#include "robot_ultrasonic.h"
#include "cmsis_os.h"
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include "modbus_interface.h"
#include "echo_processing.h"
#include "debug_config.h"

// 外部变量声明
extern modbusHandler_t ModbusH2;

// 距离阈值定义
#define ROBOT_SAFE_DISTANCE      2000  // 2m - 安全距离
#define ROBOT_CAUTION_DISTANCE   1000  // 1m - 注意距离  
#define ROBOT_DANGER_DISTANCE    500   // 0.5m - 危险距离
#define ROBOT_CRITICAL_DISTANCE  200   // 0.2m - 紧急距离

// 更新频率定义
#define ROBOT_FREQ_STOP          5     // 停止时5Hz
#define ROBOT_FREQ_SLOW          10    // 慢速时10Hz
#define ROBOT_FREQ_NORMAL        20    // 正常时20Hz
#define ROBOT_FREQ_FAST          50    // 快速时50Hz

// 全局变量定义
RobotDirectionInfoTypedef GstrRobotDirectionInfo;
RobotConfigTypedef GstrRobotConfig;
SingleSensorObjectInfo GstrRobotSensorInfo[ROBOT_SENSOR_NUMBER];
uint8_t Gu8RobotSysDistance[ROBOT_SENSOR_NUMBER] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

// E52436传感器三角定位配置：每个传感器发波时的相邻接收传感器
typedef struct {
    uint8_t master_sensor;    // 主发波传感器
    uint8_t left_sensor;      // 左侧接收传感器
    uint8_t right_sensor;     // 右侧接收传感器
    uint8_t direction;        // 对应的机器人方向
    float sensor_spacing;     // 传感器间距(cm)
    float angle_offset;       // 角度偏移(度)
    uint8_t priority;         // 优先级(1=最高)
} E52436_TriangleConfig_t;

// 传感器物理布局参数 (方形底盘8传感器)
#define SENSOR_SPACING_CM       10.0f    // 相邻传感器间距
#define SENSOR_DIAGONAL_CM      14.14f   // 对角传感器间距 (10*√2)
#define TRIANGLE_MIN_DISTANCE   5.0f     // 三角测量最小距离
#define TRIANGLE_MAX_DISTANCE   200.0f   // 三角测量最大距离

// 方形底盘传感器布局 (0-7对应1-8号传感器)
//    7 ---- 0 ---- 1
//    |             |
//    6             2
//    |             |
//    5 ---- 4 ---- 3

// E52436三角定位配置表 (8传感器方形底盘)
static const E52436_TriangleConfig_t e52436_configs[] = {
    // 前面 (0-1号传感器)
    {0, 7, 1, ROBOT_DIRECTION_FORWARD, SENSOR_SPACING_CM, 0.0f, 1},    // 0号发波，7号和1号监听
    {1, 0, 2, ROBOT_DIRECTION_FORWARD, SENSOR_SPACING_CM, 0.0f, 1},    // 1号发波，0号和2号监听

    // 右面 (2-3号传感器)
    {2, 1, 3, ROBOT_DIRECTION_RIGHT, SENSOR_SPACING_CM, 0.0f, 1},      // 2号发波，1号和3号监听
    {3, 2, 4, ROBOT_DIRECTION_RIGHT, SENSOR_SPACING_CM, 0.0f, 1},      // 3号发波，2号和4号监听

    // 后面 (4-5号传感器)
    {4, 3, 5, ROBOT_DIRECTION_BACKWARD, SENSOR_SPACING_CM, 0.0f, 1},   // 4号发波，3号和5号监听
    {5, 4, 6, ROBOT_DIRECTION_BACKWARD, SENSOR_SPACING_CM, 0.0f, 1},   // 5号发波，4号和6号监听

    // 左面 (6-7号传感器)
    {6, 5, 7, ROBOT_DIRECTION_LEFT, SENSOR_SPACING_CM, 0.0f, 1},       // 6号发波，5号和7号监听
    {7, 6, 0, ROBOT_DIRECTION_LEFT, SENSOR_SPACING_CM, 0.0f, 1},       // 7号发波，6号和0号监听
};

#define E52436_CONFIG_COUNT (sizeof(e52436_configs)/sizeof(e52436_configs[0]))

// 三角测量结果结构
typedef struct {
    float distance;           // 计算出的距离
    float angle;             // 计算出的角度
    uint8_t confidence;      // 置信度(0-100)
    uint8_t valid;           // 结果有效性
    uint16_t master_echo;    // 主发回波时间
    uint16_t left_echo;      // 左侧回波时间
    uint16_t right_echo;     // 右侧回波时间
} TriangleResult_t;

// 方向轮询发波模式 (兼容4传感器模式)
static const uint16_t direction_burst_patterns[ROBOT_DIRECTION_NUMBER] = {
    0x03,  // 前方: F1+F2 (bit0+bit1)
    0x0C,  // 右侧: R1+R2 (bit2+bit3)
    0x0C,  // 后方: R1+R2 (bit2+bit3) - 复用右侧
    0x03   // 左侧: F1+F2 (bit0+bit1) - 复用前方
};

// 单传感器发波模式
static const uint16_t single_burst_patterns[ROBOT_SENSOR_NUMBER] = {
    0x01, 0x02, 0x04, 0x08,  // F1,F2,R1,R2
    0x10, 0x20, 0x40, 0x80   // B1,B2,L1,L2
};

/**
 * @brief E52436三角测量算法
 * @param config 传感器配置
 * @param master_echo 主发传感器回波时间
 * @param left_echo 左侧传感器回波时间
 * @param right_echo 右侧传感器回波时间
 * @return 三角测量结果
 */
TriangleResult_t E52436_TriangleMeasurement(const E52436_TriangleConfig_t* config,
                                           uint16_t master_echo,
                                           uint16_t left_echo,
                                           uint16_t right_echo)
{
    TriangleResult_t result = {0};

    // 1. 回波时间有效性检查
    uint8_t master_valid = (master_echo < 20000 && master_echo > 100);
    uint8_t left_valid = (config->left_sensor != 0xFF) && (left_echo < 20000 && left_echo > 100);
    uint8_t right_valid = (config->right_sensor != 0xFF) && (right_echo < 20000 && right_echo > 100);

    if(!master_valid) {
        printf("Triangle[%d]: Master echo invalid (%d)\r\n", config->master_sensor, master_echo);
        return result;
    }

    // 2. 主发距离计算 (基础距离)
    float master_distance = master_echo / 58.0f;

    // 3. 智能回波可靠性评估和选择
    typedef struct {
        uint16_t echo_time;
        float distance;
        uint8_t reliability_score;
        uint8_t is_left;
        const char* name;
    } EchoCandidate_t;

    EchoCandidate_t candidates[3] = {0};
    uint8_t candidate_count = 0;

    // 主发回波 (总是可用)
    candidates[candidate_count].echo_time = master_echo;
    candidates[candidate_count].distance = master_distance;
    candidates[candidate_count].reliability_score = 60; // 基础分数
    candidates[candidate_count].is_left = 0;
    candidates[candidate_count].name = "MASTER";
    candidate_count++;

    // 左侧监听回波
    if(left_valid) {
        float left_distance = left_echo / 58.0f;
        uint8_t score = 50; // 基础分数

        // 距离合理性检查
        if(left_distance >= 5 && left_distance <= 200) score += 20;

        // 与主发回波的一致性检查
        float distance_diff = fabs(left_distance - master_distance);
        if(distance_diff < 5) score += 20;      // 差异<5cm，很好
        else if(distance_diff < 15) score += 10; // 差异<15cm，还行

        // 回波时间合理性
        if(left_echo > 100 && left_echo < 15000) score += 10;

        candidates[candidate_count].echo_time = left_echo;
        candidates[candidate_count].distance = left_distance;
        candidates[candidate_count].reliability_score = score;
        candidates[candidate_count].is_left = 1;
        candidates[candidate_count].name = "LEFT";
        candidate_count++;
    }

    // 右侧监听回波
    if(right_valid) {
        float right_distance = right_echo / 58.0f;
        uint8_t score = 50; // 基础分数

        // 距离合理性检查
        if(right_distance >= 5 && right_distance <= 200) score += 20;

        // 与主发回波的一致性检查
        float distance_diff = fabs(right_distance - master_distance);
        if(distance_diff < 5) score += 20;      // 差异<5cm，很好
        else if(distance_diff < 15) score += 10; // 差异<15cm，还行

        // 回波时间合理性
        if(right_echo > 100 && right_echo < 15000) score += 10;

        candidates[candidate_count].echo_time = right_echo;
        candidates[candidate_count].distance = right_distance;
        candidates[candidate_count].reliability_score = score;
        candidates[candidate_count].is_left = 0;
        candidates[candidate_count].name = "RIGHT";
        candidate_count++;
    }

    // 选择最可靠的监听回波
    EchoCandidate_t* best_listen = NULL;
    uint8_t best_score = 0;

    for(uint8_t i = 1; i < candidate_count; i++) { // 跳过主发回波(索引0)
        if(candidates[i].reliability_score > best_score) {
            best_score = candidates[i].reliability_score;
            best_listen = &candidates[i];
        }
    }

    printf("Triangle[%d]: Echo candidates - ", config->master_sensor);
    for(uint8_t i = 0; i < candidate_count; i++) {
        printf("%s:%.1fcm(score=%d) ", candidates[i].name, candidates[i].distance, candidates[i].reliability_score);
    }
    printf("\r\n");

    if(best_listen == NULL || best_score < 60) {
        // 没有可靠的监听回波，使用主发距离
        result.distance = master_distance;
        result.angle = 0;
        result.confidence = 50;
        result.valid = (master_distance >= TRIANGLE_MIN_DISTANCE && master_distance <= TRIANGLE_MAX_DISTANCE);
        printf("Triangle[%d]: No reliable listen echo, using master distance %.1fcm\r\n",
               config->master_sensor, master_distance);
        return result;
    }

    // 4. 执行三角测量计算
    float listen_distance = best_listen->distance;
    float distance_diff = listen_distance - master_distance;

    // 使用几何关系计算精确距离和角度
    float sensor_spacing = config->sensor_spacing;
    float angle_rad = atan2(distance_diff, sensor_spacing);
    float corrected_distance;

    // 根据监听传感器位置调整计算
    if(best_listen->is_left) {
        // 左侧监听：负角度
        angle_rad = -fabs(angle_rad);
        corrected_distance = master_distance * cos(angle_rad);
    } else {
        // 右侧监听：正角度
        angle_rad = fabs(angle_rad);
        corrected_distance = master_distance * cos(angle_rad);
    }

    // 5. 多种测量方法融合
    float final_distance;
    uint8_t final_confidence;

    if(candidate_count >= 3 && left_valid && right_valid) {
        // 三点测量：主发+左右监听
        float left_distance = candidates[1].distance;
        float right_distance = candidates[2].distance;

        // 检查三点一致性
        float max_diff = fmax(fmax(fabs(master_distance - left_distance),
                                  fabs(master_distance - right_distance)),
                             fabs(left_distance - right_distance));

        if(max_diff < 10) {
            // 三点一致性好，取加权平均
            final_distance = (master_distance * 0.5 + left_distance * 0.25 + right_distance * 0.25);
            final_confidence = 95;
            printf("Triangle[%d]: 3-point consistent (diff=%.1fcm), weighted avg=%.1fcm\r\n",
                   config->master_sensor, max_diff, final_distance);
        } else {
            // 三点不一致，使用最可靠的双点测量
            final_distance = corrected_distance;
            final_confidence = best_score;
            printf("Triangle[%d]: 3-point inconsistent (diff=%.1fcm), using best 2-point=%.1fcm\r\n",
                   config->master_sensor, max_diff, final_distance);
        }
    } else {
        // 双点测量：主发+最佳监听
        final_distance = corrected_distance;
        final_confidence = best_score;
        printf("Triangle[%d]: 2-point measurement, distance=%.1fcm\r\n",
               config->master_sensor, final_distance);
    }

    // 6. 结果验证和输出
    if(final_distance >= TRIANGLE_MIN_DISTANCE && final_distance <= TRIANGLE_MAX_DISTANCE) {
        result.distance = final_distance;
        result.angle = angle_rad * 180.0f / 3.14159f;
        result.confidence = final_confidence;
        result.valid = 1;

        printf("Triangle[%d]: FINAL - Master=%.1fcm, %s=%.1fcm, Result=%.1fcm, Angle=%.1f°, Conf=%d%%\r\n",
               config->master_sensor, master_distance, best_listen->name, listen_distance,
               final_distance, result.angle, result.confidence);
    } else {
        printf("Triangle[%d]: Result out of range (%.1fcm), using master distance\r\n",
               config->master_sensor, final_distance);
        result.distance = master_distance;
        result.angle = 0;
        result.confidence = 50;
        result.valid = 1;
    }

    // 记录原始数据
    result.master_echo = master_echo;
    result.left_echo = left_echo;
    result.right_echo = right_echo;

    return result;
}


/**
 * @brief 调试：打印原始传感器数据
 */
void Robot_DebugPrintSensorData(void)
{
    DEBUG_ROBOT("=== Raw Sensor Data Debug ===\r\n");
    for(uint8_t i = 0; i < SENSOR_NUMBER; i++) {
        DEBUG_ROBOT("Gu8SysDistance[%d] = %d (0x%02X)\r\n",
                    i, Gu8SysDistance[i], Gu8SysDistance[i]);
    }
    DEBUG_ROBOT("NO_OBJECT = %d (0x%02X)\r\n", NO_OBJECT, NO_OBJECT);
    DEBUG_ROBOT("=============================\r\n");

    // 关键系统信息 - 始终输出
    DEBUG_CRITICAL("SensorData: [%d,%d,%d,%d] Status: [%d,%d,%d,%d]\r\n",
                   Gu8SysDistance[0], Gu8SysDistance[1], Gu8SysDistance[2], Gu8SysDistance[3],
                   Gu8SensorErrSta[0], Gu8SensorErrSta[1], Gu8SensorErrSta[2], Gu8SensorErrSta[3]);
}

/**
 * @brief 机器人超声波系统初始化
 */
void Robot_UltrasonicInit(void)
{
    // 初始化配置参数
    GstrRobotConfig.u16MinSafeDistance = ROBOT_CRITICAL_DISTANCE;
    GstrRobotConfig.u16MaxDetectDistance = 5000; // 5m
    GstrRobotConfig.u8UpdateFrequency = ROBOT_FREQ_NORMAL;
    GstrRobotConfig.eMotionState = ROBOT_MOTION_STOP;
    GstrRobotConfig.u8EnableAdaptiveMode = 1;
    
    // 初始化方向信息
    memset(&GstrRobotDirectionInfo, 0, sizeof(RobotDirectionInfoTypedef));
    for(uint8_t i = 0; i < ROBOT_DIRECTION_NUMBER; i++) {
        GstrRobotDirectionInfo.u16Distance[i] = 0xFFFF; // 无障碍物
        GstrRobotDirectionInfo.u8Confidence[i] = 0;
        GstrRobotDirectionInfo.u8WarningLevel[i] = ROBOT_WARNING_SAFE;
    }
    
    // 初始化传感器信息
    memset(GstrRobotSensorInfo, 0, sizeof(GstrRobotSensorInfo));
    
    printf("Robot Ultrasonic System Initialized\r\n");
}


/**
 * @brief E52436三角定位处理 (完整版)
 */
void Robot_ProcessE52436Triangle(uint8_t config_index)
{
    if(config_index >= E52436_CONFIG_COUNT) return;

    const E52436_TriangleConfig_t* config = &e52436_configs[config_index];
    uint8_t master_sensor = config->master_sensor;
    uint16_t burst_pattern = single_burst_patterns[master_sensor];

    printf("=== E52436 Triangle[%d]: Master=%d, Left=%d, Right=%d, Priority=%d ===\r\n",
           config_index, master_sensor, config->left_sensor, config->right_sensor, config->priority);

    // 1. 设置单传感器发波模式
    if(US_SetBurstMode(&ModbusH2, burst_pattern) != MODBUS_Q_OK) {
        printf("Failed to set burst mode for sensor %d\r\n", master_sensor);
        return;
    }

    // 2. 等待测距完成
    osDelay(50);

    // 3. 读取所有相关传感器的回波数据
    uint16_t master_echo = 0xFFFF, left_echo = 0xFFFF, right_echo = 0xFFFF;

    // 读取主发传感器
    uint8_t master_modbus_id = master_sensor + 1;
    SensorEchoInfoTypedef master_data;
    if(US_ReadEchoInfo(&ModbusH2, master_modbus_id, &master_data) == MODBUS_Q_OK) {
        master_echo = master_data.EchoInfo[0].u16EchoTime;
        printf("Master Sensor%d: EchoTime=%d\r\n", master_sensor, master_echo);

        // 处理主发传感器数据 (用于算法处理)
        UltrasonicAlg_ProcessData(&master_data, (tenu_SensorIDTypedef)master_sensor);
    } else {
        printf("Failed to read master sensor %d\r\n", master_sensor);
        return;
    }

    // 读取左侧接收传感器
    if(config->left_sensor != 0xFF) {
        uint8_t left_modbus_id = config->left_sensor + 1;
        SensorEchoInfoTypedef left_data;
        if(US_ReadEchoInfo(&ModbusH2, left_modbus_id, &left_data) == MODBUS_Q_OK) {
            left_echo = left_data.EchoInfo[0].u16EchoTime;
            printf("Left Sensor%d: EchoTime=%d\r\n", config->left_sensor, left_echo);
        }
    }

    // 读取右侧接收传感器
    if(config->right_sensor != 0xFF) {
        uint8_t right_modbus_id = config->right_sensor + 1;
        SensorEchoInfoTypedef right_data;
        if(US_ReadEchoInfo(&ModbusH2, right_modbus_id, &right_data) == MODBUS_Q_OK) {
            right_echo = right_data.EchoInfo[0].u16EchoTime;
            printf("Right Sensor%d: EchoTime=%d\r\n", config->right_sensor, right_echo);
        }
    }

    // 4. 执行三角测量
    TriangleResult_t triangle_result = E52436_TriangleMeasurement(config, master_echo, left_echo, right_echo);

    // 5. 更新传感器距离数据 (如果三角测量有效且置信度高)
    if(triangle_result.valid && triangle_result.confidence >= 60) {
        uint8_t new_distance = (uint8_t)(triangle_result.distance + 0.5f); // 四舍五入
        if(new_distance >= 5 && new_distance <= 200) {
            printf("Triangle UPDATE: Sensor%d %dcm -> %dcm (Conf=%d%%, Angle=%.1f°)\r\n",
                   master_sensor, Gu8SysDistance[master_sensor], new_distance,
                   triangle_result.confidence, triangle_result.angle);
            Gu8SysDistance[master_sensor] = new_distance;
        }
    }

    printf("Final: Sensor%d Distance=%dcm, Confidence=%d%%\r\n",
           master_sensor, Gu8SysDistance[master_sensor], triangle_result.confidence);
}


/**
 * @brief 机器人超声波主任务
 */
void Robot_UltrasonicTask(void)
{
    static uint8_t sensor_index = 0;
    static uint32_t cycle_count = 0;

    Robot_UltrasonicInit();

    printf("=== E52436 Ultrasonic System Started ===\r\n");
    printf("Sensor sequence: 0->1->2->3 (single burst mode)\r\n");

    for(;;) {
        uint32_t start_time = osKernelSysTick();

        // 1. 获取当前运动状态
        tenu_RobotMotionStateTypedef motion_state = Robot_GetMotionState();

        // 2. E52436单传感器轮询模式
        printf("\r\n=== Sensor %d Burst Cycle %lu ===\r\n", sensor_index, cycle_count);
        Robot_ProcessE52436Triangle(sensor_index);

        // 3. 更新传感器索引
        sensor_index = (sensor_index + 1) % E52436_CONFIG_COUNT;

        // 4. 完成一轮传感器扫描后进行全局处理
        if(sensor_index == 0) {
            cycle_count++;

            // 更新所有方向的融合数据
            for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
                Robot_DirectionDataFusion(dir);
            }

            Robot_GlobalProcessing();

            if(cycle_count % 4 == 0) {
                printf("E52436 Cycle %lu completed (4 sensors scanned)\r\n", cycle_count);
            }
        }

        // 5. 动态延时控制
        uint16_t delay_ms = Robot_GetAdaptiveDelay(motion_state);
        uint32_t elapsed = osKernelSysTick() - start_time;
        if(delay_ms > elapsed) {
            osDelay(delay_ms - elapsed);
        }
    }
}


/**
 * @brief 处理指定方向的传感器数据 (兼容接口)
 */
void Robot_ProcessDirection(uint8_t direction, uint16_t burst_pattern)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;

    printf("=== Processing Direction %d ===\r\n", direction);

    // 根据方向找到对应的E52436配置并处理
    for(uint8_t i = 0; i < E52436_CONFIG_COUNT; i++) {
        if(e52436_configs[i].direction == direction) {
            Robot_ProcessE52436Triangle(i);
        }
    }

    // 方向级数据融合
    Robot_DirectionDataFusion(direction);

    // 更新时间戳
    GstrRobotDirectionInfo.u32LastUpdateTime[direction] = osKernelSysTick();
}

/**
 * @brief 方向级数据融合 (E52436三角定位模式)
 */
void Robot_DirectionDataFusion(uint8_t direction)
{
    if(direction >= ROBOT_DIRECTION_NUMBER) return;

    printf("=== Direction %d Data Fusion ===\r\n", direction);

    // 收集该方向的所有传感器数据，按优先级排序
    typedef struct {
        uint8_t sensor_id;
        uint8_t priority;
        uint16_t distance_mm;
        uint8_t confidence;
        uint8_t valid;
    } SensorData_t;

    SensorData_t sensors[4] = {0};
    uint8_t sensor_count = 0;

    // 收集该方向的传感器数据
    for(uint8_t i = 0; i < E52436_CONFIG_COUNT; i++) {
        if(e52436_configs[i].direction == direction && sensor_count < 4) {
            uint8_t sensor_id = e52436_configs[i].master_sensor;
            uint8_t distance_cm = Gu8SysDistance[sensor_id];

            if(distance_cm != NO_OBJECT && distance_cm >= 5 && distance_cm <= 200) {
                sensors[sensor_count].sensor_id = sensor_id;
                sensors[sensor_count].priority = e52436_configs[i].priority;
                sensors[sensor_count].distance_mm = distance_cm * 58;
                sensors[sensor_count].confidence = (e52436_configs[i].priority == 1) ? 90 : 70;
                sensors[sensor_count].valid = 1;
                sensor_count++;

                printf("Sensor%d: %dcm (Priority=%d, Conf=%d%%)\r\n",
                       sensor_id, distance_cm, e52436_configs[i].priority, sensors[sensor_count-1].confidence);
            }
        }
    }

    if(sensor_count == 0) {
        printf("Direction %d: No valid sensors\r\n", direction);
//        GstrRobotDirectionInfo.u16FusedDistance[direction] = 0xFFFF;
        GstrRobotDirectionInfo.u8Confidence[direction] = 0;
        return;
    }

    // 智能融合算法：基于优先级和置信度
    uint16_t fused_distance = 0xFFFF;
    uint8_t fused_confidence = 0;

    if(sensor_count == 1) {
        // 单传感器：直接使用
        fused_distance = sensors[0].distance_mm;
        fused_confidence = sensors[0].confidence;
        printf("Single sensor fusion: %dmm (Conf=%d%%)\r\n", fused_distance, fused_confidence);

    } else if(sensor_count >= 2) {
        // 多传感器：优先级加权融合

        // 找到最高优先级
        uint8_t highest_priority = 255;
        for(uint8_t i = 0; i < sensor_count; i++) {
            if(sensors[i].priority < highest_priority) {
                highest_priority = sensors[i].priority;
            }
        }

        // 收集最高优先级的传感器
        uint16_t priority_distances[4];
        uint8_t priority_count = 0;
        uint16_t total_confidence = 0;

        for(uint8_t i = 0; i < sensor_count; i++) {
            if(sensors[i].priority == highest_priority) {
                priority_distances[priority_count] = sensors[i].distance_mm;
                total_confidence += sensors[i].confidence;
                priority_count++;
            }
        }

        if(priority_count == 1) {
            // 单个最高优先级传感器
            fused_distance = priority_distances[0];
            fused_confidence = total_confidence;

        } else {
            // 多个同优先级传感器：检查一致性
            uint16_t min_dist = priority_distances[0];
            uint16_t max_dist = priority_distances[0];
            uint32_t sum_dist = priority_distances[0];

            for(uint8_t i = 1; i < priority_count; i++) {
                if(priority_distances[i] < min_dist) min_dist = priority_distances[i];
                if(priority_distances[i] > max_dist) max_dist = priority_distances[i];
                sum_dist += priority_distances[i];
            }

            uint16_t diff = max_dist - min_dist;
            if(diff < 580) { // 差异小于10cm，取平均值
                fused_distance = sum_dist / priority_count;
                fused_confidence = total_confidence / priority_count + 10; // 一致性奖励
            } else {
                // 差异较大，取最小值（保守策略）
                fused_distance = min_dist;
                fused_confidence = total_confidence / priority_count - 10; // 不一致性惩罚
            }
        }

        printf("Multi-sensor fusion: %dmm (Conf=%d%%, Sensors=%d, Priority=%d)\r\n",
               fused_distance, fused_confidence, priority_count, highest_priority);
    }

    // 限制置信度范围
    if(fused_confidence > 100) fused_confidence = 100;
    if(fused_confidence <= 0) fused_confidence = 0;

    // 更新方向距离和置信度
    GstrRobotDirectionInfo.u16Distance[direction] = fused_distance;
    GstrRobotDirectionInfo.u8Confidence[direction] = fused_confidence;

    printf("Direction %d Final: Distance=%dmm, Confidence=%d%%\r\n",
           direction, fused_distance, fused_confidence);
}

/**
 * @brief 选择发波策略
 */
uint16_t Robot_SelectBurstPattern(tenu_RobotMotionStateTypedef motion_state, uint8_t direction_index)
{
    if(!GstrRobotConfig.u8EnableAdaptiveMode) {
        // 非自适应模式，正常轮询
        return direction_burst_patterns[direction_index];
    }
    
    switch(motion_state) {
        case ROBOT_MOTION_FORWARD:
            // 前进时优先前方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_FORWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_BACKWARD:
            // 后退时优先后方，然后左右侧
            if(direction_index == ROBOT_DIRECTION_BACKWARD) 
                return direction_burst_patterns[ROBOT_DIRECTION_BACKWARD];
            if(direction_index % 2 == 1) 
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            
        case ROBOT_MOTION_TURN_LEFT:
            // 左转时优先左侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_LEFT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        case ROBOT_MOTION_TURN_RIGHT:
            // 右转时优先右侧和前方
            if(direction_index <= 1)
                return direction_burst_patterns[ROBOT_DIRECTION_RIGHT];
            return direction_burst_patterns[ROBOT_DIRECTION_FORWARD];
            
        default: // ROBOT_MOTION_STOP
            // 停止时正常轮询
            return direction_burst_patterns[direction_index];
    }
}

/**
 * @brief 获取自适应延时
 */
uint16_t Robot_GetAdaptiveDelay(tenu_RobotMotionStateTypedef motion_state)
{
    switch(motion_state) {
        case ROBOT_MOTION_STOP:
            return 1000 / ROBOT_FREQ_STOP;      // 200ms
        case ROBOT_MOTION_FORWARD:
        case ROBOT_MOTION_BACKWARD:
            return 1000 / ROBOT_FREQ_NORMAL;    // 50ms
        case ROBOT_MOTION_TURN_LEFT:
        case ROBOT_MOTION_TURN_RIGHT:
            return 1000 / ROBOT_FREQ_FAST;      // 20ms
        default:
            return 1000 / ROBOT_FREQ_SLOW;      // 100ms
    }
}

/**
 * @brief 更新警告系统
 */
void Robot_UpdateWarningSystem(void)
{
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint16_t distance = GstrRobotDirectionInfo.u16Distance[dir];

        if(distance == 0xFFFF || distance > ROBOT_SAFE_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_SAFE;
        } else if(distance > ROBOT_CAUTION_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CAUTION;
        } else if(distance > ROBOT_DANGER_DISTANCE) {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_DANGER;
        } else {
            GstrRobotDirectionInfo.u8WarningLevel[dir] = ROBOT_WARNING_CRITICAL;
        }
    }
}

/**
 * @brief 全局数据处理
 */
void Robot_GlobalProcessing(void)
{

    // 1. 调试：打印原始传感器数据
    Robot_DebugPrintSensorData();

    // 1. 更新警告系统
    Robot_UpdateWarningSystem();

    // 2. 检查传感器状态
    uint8_t fault_count = 0;
    for(uint8_t i = 0; i < ROBOT_SENSOR_NUMBER; i++) {
        if(GstrRobotDirectionInfo.u8SensorStatus[i] == 0) {
            fault_count++;
        }
    }

    // 3. 输出状态报告
    printf("=== Robot Status Report ===\r\n");
    printf("Motion State: %d\r\n", GstrRobotConfig.eMotionState);
    printf("Sensor Faults: %d/%d\r\n", fault_count, ROBOT_SENSOR_NUMBER);

    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        const char* dir_names[] = {"Forward", "Right", "Backward", "Left"};
        printf("%s: %dmm (Warning:%d, Confidence:%d%%)\r\n",
               dir_names[dir],
               GstrRobotDirectionInfo.u16Distance[dir],
               GstrRobotDirectionInfo.u8WarningLevel[dir],
               GstrRobotDirectionInfo.u8Confidence[dir]);
    }
    printf("===========================\r\n");
}

/**
 * @brief 获取机器人运动状态
 */
tenu_RobotMotionStateTypedef Robot_GetMotionState(void)
{
    return GstrRobotConfig.eMotionState;
}

/**
 * @brief 设置机器人运动状态
 */
void Robot_SetMotionState(tenu_RobotMotionStateTypedef motion_state)
{
    GstrRobotConfig.eMotionState = motion_state;
    printf("Robot motion state changed to: %d\r\n", motion_state);
}

/**
 * @brief 获取指定方向的距离
 */
uint16_t Robot_GetDirectionDistance(tenu_RobotDirectionTypedef direction)
{
    return GstrRobotDirectionInfo.u16Distance[direction];
}

/**
 * @brief 获取指定方向的警告等级
 */
tenu_RobotWarningLevelTypedef Robot_GetDirectionWarningLevel(tenu_RobotDirectionTypedef direction)
{
    return (tenu_RobotWarningLevelTypedef)GstrRobotDirectionInfo.u8WarningLevel[direction];
}

/**
 * @brief 检查是否可以安全移动
 */
uint8_t Robot_IsSafeToMove(tenu_RobotDirectionTypedef direction)
{

    uint16_t distance = GstrRobotDirectionInfo.u16Distance[direction];
    uint8_t confidence = GstrRobotDirectionInfo.u8Confidence[direction];

    // 距离足够且数据可信度高
    if(distance > GstrRobotConfig.u16MinSafeDistance && confidence > 50) {
        return 1;
    }

    return 0;
}

/**
 * @brief 计算数据可信度
 */
uint8_t Robot_CalculateConfidence(uint8_t valid1, uint8_t valid2, uint16_t dist1, uint16_t dist2)
{
    if(!valid1 && !valid2) return 0;
    if(valid1 && !valid2) return 60;
    if(!valid1 && valid2) return 60;

    // 两个传感器都有效
    uint16_t diff = (dist1 > dist2) ? (dist1 - dist2) : (dist2 - dist1);
    if(diff < 50) return 95;      // 差异<5cm，高可信度
    if(diff < 100) return 80;     // 差异<10cm，较高可信度
    if(diff < 200) return 65;     // 差异<20cm，中等可信度
    return 50;                    // 差异较大，低可信度
}
