#include "algorithm.h"
#include "echo_processing.h"
#include "triangulation.h"

void Algorithm_Init(void)
{
    UltrasonicAlg_Init();
}

void Algorithm_ProcessFrame(SensorEchoInfoTypedef* master_data,
                          SensorEchoInfoTypedef* slave_data,
                          float* obj_x,
                          float* obj_y)
{
    // 处理主探头数据 - 默认使用SENSOR_RCL作为主探头
    UltrasonicAlg_ProcessData(master_data, SENSOR_RCL);

    // 处理从探头数据
    //UltrasonicAlg_ProcessData(slave_data, SENSOR_RCR);

    // 执行三角测量计算
    Triangulation_CalculatePosition(master_data, slave_data, obj_x, obj_y);

    // 单位转换：m转mm
    *obj_x *= 1000.0f;
    *obj_y *= 1000.0f;
}
