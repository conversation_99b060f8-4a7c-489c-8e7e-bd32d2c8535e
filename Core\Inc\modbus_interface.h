#ifndef MODBUS_INTERFACE_H
#define MODBUS_INTERFACE_H

#include "Modbus.h"
#include "stdint.h"
#include "echo_processing.h"

#define MODBUS_Q_OK     0
#define MODBUS_Q_ERR    1

// E52436 Modbus寄存器映射 (基于原项目分析)
#define REG_DISTANCE     0x0010  // 测距值寄存器
#define REG_ADDRESS      0x0015  // 从机地址寄存器
#define REG_BAUDRATE     0x000A  // 波特率寄存器
#define REG_BURST_CTRL   0x0011  // 突发控制寄存器
#define REG_ECHO_INFO    0x0012  // 回波信息寄存器 (修正地址)
#define REG_TEMPERATURE  0x0021  // 温度寄存器
#define REG_VOLTAGE      0x0022  // 电压寄存器

// 回波信息寄存器详细映射 (基于E52436项目分析)
#define REG_ECHO_RINGING_TIME    0x0012  // 余震时间
#define REG_ECHO_SAMPLE_FREQ     0x0012  // 采样频率
#define REG_ECHO_NOISE_CNT       0x0013  // 噪声计数
#define REG_ECHO_STATUS_FLAG     0x0014  // 状态标志
#define REG_ECHO_DATA_START      0x0015  // 回波数据起始地址

// 每个回波占用4个寄存器：时间、宽度、电压、置信度
#define ECHO_REGS_PER_ECHO       4
#define MAX_ECHO_READ            3       // 最多读取3个回波

// 电压等级枚举
typedef enum {
    VOLTAGE_70 = 134,
    VOLTAGE_75 = 136,
    VOLTAGE_85 = 150,
    VOLTAGE_88 = 158,
    VOLTAGE_162 = 365,
    VOLTAGE_165 = 371,
    VOLTAGE_175 = 398,
    VOLTAGE_180 = 411
} VoltageLevel_t;




// 读取测距值 (mm)
int16_t read_distance(modbusHandler_t *modH, uint8_t slave_addr, uint16_t *distance);

// 读取多个寄存器
int16_t read_multiple_registers(modbusHandler_t *modH, uint8_t slave_addr, 
                              uint16_t start_reg, uint16_t num_regs, uint16_t *data);

// 设置从机地址
int16_t set_slave_address(modbusHandler_t *modH, uint8_t old_addr, uint8_t new_addr);

// 设置波特率
int16_t set_baudrate(modbusHandler_t *modH, uint8_t slave_addr, uint8_t baud_level);

// 处理非法操作
void handle_invalid_operation(modbusHandler_t *modH);

// 清理Modbus状态
void ClearModbusState(modbusHandler_t *modH);

uint8_t US_SetBurstMode(modbusHandler_t *modH, uint16_t master_mask);

// 示例5: 非法操作处理(演示)
void handle_invalid_operation(modbusHandler_t *modH);

uint8_t US_ReadTemperature(modbusHandler_t *modH, uint8_t addr, float *temp);

// 读取完整回波信息 (包括多个回波)
uint8_t US_ReadEchoInfo(modbusHandler_t *modH, uint8_t addr, SensorEchoInfoTypedef *echo);

// 读取单个回波数据
uint8_t US_ReadSingleEcho(modbusHandler_t *modH, uint8_t addr, uint8_t echo_index, EchoInfoTypedef *echo_info);

// 读取电压值
uint8_t US_ReadVoltage(modbusHandler_t *modH, uint8_t addr, float *voltage, uint8_t retry);

// Modbus通信故障处理函数
void ModbusCommunicationFallback(SensorEchoInfoTypedef *echo, uint8_t addr);
void UpdateLastValidData(const SensorEchoInfoTypedef *echo, uint8_t addr);

#endif
