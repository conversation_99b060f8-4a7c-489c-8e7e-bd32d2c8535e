/**
 * @file robot_ultrasonic_example.c
 * @brief 机器人底盘8传感器超声波测距系统使用示例
 * @date 2024-07-08
 */

#include "robot_ultrasonic.h"
#include "cmsis_os.h"
#include <stdio.h>
#include <stdlib.h> // For abs

/**
 * @brief 机器人导航控制示例
 */
void Robot_NavigationExample(void)
{
    printf("=== Robot Navigation Example ===\r\n");
    
    // 1. 检查前方是否可以前进
    if(Robot_IsSafeToMove(ROBOT_DIRECTION_FORWARD)) {
        printf("Forward path is clear, distance: %dmm\r\n", 
               Robot_GetDirectionDistance(ROBOT_DIRECTION_FORWARD));
        
        // 设置前进状态
        Robot_SetMotionState(ROBOT_MOTION_FORWARD);
    } else {
        printf("Forward path blocked! Distance: %dmm, Warning: %d\r\n",
               Robot_GetDirectionDistance(ROBOT_DIRECTION_FORWARD),
               Robot_GetDirectionWarningLevel(ROBOT_DIRECTION_FORWARD));
        
        // 检查左右是否可以转向
        if(Robot_IsSafeToMove(ROBOT_DIRECTION_LEFT)) {
            printf("Turning left...\r\n");
            Robot_SetMotionState(ROBOT_MOTION_TURN_LEFT);
        } else if(Robot_IsSafeToMove(ROBOT_DIRECTION_RIGHT)) {
            printf("Turning right...\r\n");
            Robot_SetMotionState(ROBOT_MOTION_TURN_RIGHT);
        } else {
            printf("All paths blocked, stopping...\r\n");
            Robot_SetMotionState(ROBOT_MOTION_STOP);
        }
    }
}

/**
 * @brief 停车辅助示例
 */
void Robot_ParkingAssistExample(void)
{
    printf("=== Parking Assist Example ===\r\n");
    
    // 设置倒车状态
    Robot_SetMotionState(ROBOT_MOTION_BACKWARD);
    
    // 监控后方距离
    uint16_t rear_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_BACKWARD);
    tenu_RobotWarningLevelTypedef warning = Robot_GetDirectionWarningLevel(ROBOT_DIRECTION_BACKWARD);
    
    switch(warning) {
        case ROBOT_WARNING_SAFE:
            printf("Parking: Safe to continue, distance: %dmm\r\n", rear_distance);
            break;
            
        case ROBOT_WARNING_CAUTION:
            printf("Parking: Caution! Obstacle detected at %dmm\r\n", rear_distance);
            break;
            
        case ROBOT_WARNING_DANGER:
            printf("Parking: Danger! Slow down, distance: %dmm\r\n", rear_distance);
            break;
            
        case ROBOT_WARNING_CRITICAL:
            printf("Parking: STOP! Critical distance: %dmm\r\n", rear_distance);
            Robot_SetMotionState(ROBOT_MOTION_STOP);
            break;
    }
}

/**
 * @brief 通道宽度检测示例
 */
void Robot_ChannelWidthExample(void)
{
    printf("=== Channel Width Detection Example ===\r\n");
    
    uint16_t left_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_LEFT);
    uint16_t right_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_RIGHT);
    
    // 计算通道宽度（假设机器人宽度为600mm）
    uint16_t robot_width = 600;
    uint16_t channel_width = left_distance + right_distance + robot_width;
    
    printf("Channel width: %dmm (Left: %dmm, Right: %dmm)\r\n",
           channel_width, left_distance, right_distance);
    
    // 判断是否可以通过
    uint16_t min_required_width = robot_width + 200; // 需要额外200mm安全距离
    
    if(channel_width > min_required_width) {
        printf("Channel is wide enough to pass through\r\n");
        
        // 检查是否居中
        int16_t offset = (int16_t)left_distance - (int16_t)right_distance;
        if(abs(offset) < 100) {
            printf("Robot is centered in channel\r\n");
        } else if(offset > 0) {
            printf("Robot is %dmm too far left\r\n", offset);
        } else {
            printf("Robot is %dmm too far right\r\n", -offset);
        }
    } else {
        printf("Channel too narrow! Required: %dmm, Available: %dmm\r\n",
               min_required_width, channel_width);
    }
}

/**
 * @brief 全方向安全检查示例
 */
void Robot_SafetyCheckExample(void)
{
    printf("=== 360° Safety Check Example ===\r\n");
    
    const char* direction_names[] = {"Forward", "Right", "Backward", "Left"};
    const char* warning_names[] = {"Safe", "Caution", "Danger", "Critical"};
    
    uint8_t safe_directions = 0;
    uint8_t critical_directions = 0;
    
    for(uint8_t dir = 0; dir < ROBOT_DIRECTION_NUMBER; dir++) {
        uint16_t distance = Robot_GetDirectionDistance((tenu_RobotDirectionTypedef)dir);
        tenu_RobotWarningLevelTypedef warning = Robot_GetDirectionWarningLevel((tenu_RobotDirectionTypedef)dir);
        
        printf("%s: %dmm [%s]\r\n", 
               direction_names[dir], distance, warning_names[warning]);
        
        if(warning == ROBOT_WARNING_SAFE) {
            safe_directions++;
        } else if(warning == ROBOT_WARNING_CRITICAL) {
            critical_directions++;
        }
    }
    
    printf("Summary: %d safe directions, %d critical directions\r\n",
           safe_directions, critical_directions);
    
    if(critical_directions > 0) {
        printf("WARNING: Critical obstacles detected!\r\n");
        Robot_SetMotionState(ROBOT_MOTION_STOP);
    } else if(safe_directions >= 2) {
        printf("Multiple safe paths available\r\n");
    } else {
        printf("Limited maneuvering space\r\n");
    }
}

/**
 * @brief 自适应速度控制示例
 */
void Robot_AdaptiveSpeedExample(void)
{
    printf("=== Adaptive Speed Control Example ===\r\n");
    
    // 获取前方距离
    uint16_t forward_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_FORWARD);
    tenu_RobotWarningLevelTypedef warning = Robot_GetDirectionWarningLevel(ROBOT_DIRECTION_FORWARD);
    
    // 根据距离调整速度
    uint8_t recommended_speed = 0; // 0-100%
    
    switch(warning) {
        case ROBOT_WARNING_SAFE:
            recommended_speed = 100; // 全速
            break;
        case ROBOT_WARNING_CAUTION:
            recommended_speed = 60;  // 减速
            break;
        case ROBOT_WARNING_DANGER:
            recommended_speed = 30;  // 慢速
            break;
        case ROBOT_WARNING_CRITICAL:
            recommended_speed = 0;   // 停止
            break;
    }
    
    printf("Forward distance: %dmm, Recommended speed: %d%%\r\n",
           forward_distance, recommended_speed);
    
    // 同时考虑侧面距离
    uint16_t left_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_LEFT);
    uint16_t right_distance = Robot_GetDirectionDistance(ROBOT_DIRECTION_RIGHT);
    
    uint16_t min_side_distance = (left_distance < right_distance) ? left_distance : right_distance;
    
    if(min_side_distance < 300) { // 侧面距离小于30cm
        recommended_speed = (recommended_speed * 50) / 100; // 再减速50%
        printf("Side clearance limited (%dmm), reducing speed to %d%%\r\n",
               min_side_distance, recommended_speed);
    }
}

/**
 * @brief 主应用示例任务
 */
void Robot_ApplicationTask(void)
{
    static uint32_t example_counter = 0;
    
    for(;;) {
        // 每5秒运行一个不同的示例
        switch(example_counter % 5) {
            case 0:
                Robot_NavigationExample();
                break;
            case 1:
                Robot_ParkingAssistExample();
                break;
            case 2:
                Robot_ChannelWidthExample();
                break;
            case 3:
                Robot_SafetyCheckExample();
                break;
            case 4:
                Robot_AdaptiveSpeedExample();
                break;
        }
        
        example_counter++;
        osDelay(5000); // 5秒间隔
    }
}

/**
 * @brief 创建机器人应用任务
 */
//void Robot_CreateApplicationTask(void)
//{
//    osThreadDef(RobotApp, Robot_ApplicationTask, osPriorityNormal, 0, 512);
//    osThreadCreate(osThread(RobotApp), NULL);
//    
//    printf("Robot Application Task Created\r\n");
//}

///**
// * @brief 机器人系统启动函数
// */
//void Robot_SystemStart(void)
//{
//    printf("Starting Robot Ultrasonic System...\r\n");
//    
//    // 1. 创建超声波测距任务
//    osThreadDef(RobotUltrasonic, Robot_UltrasonicTask, osPriorityHigh, 0, 1024);
//    osThreadCreate(osThread(RobotUltrasonic), NULL);
//    
//    // 2. 创建应用示例任务
//    Robot_CreateApplicationTask();
//    
//    printf("Robot System Started Successfully\r\n");
//}
